// Test script for County-User relationship
// Run with: node examples/test-county-user-relationship.js

const BASE_URL = 'http://localhost:3000/api'

// Test credentials
const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = 'password123'

let authToken = null
let testCountyId = null
let testUserIds = []

async function login() {
  console.log('🔐 Logging in as admin...')
  
  try {
    const response = await fetch(`${BASE_URL}/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
      }),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Login successful!')
      authToken = data.token
      return true
    } else {
      console.log('❌ Login failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Login error:', error.message)
    return false
  }
}

async function createTestCounty() {
  console.log('\n🏛️  Creating test county...')
  
  if (!authToken) {
    console.log('❌ No auth token available')
    return false
  }

  const countyData = {
    name: 'Test County for Users',
    code: 'TEST-USERS-001',
    coordinates: {
      latitude: -1.0000,
      longitude: 36.0000
    },
    description: 'A test county for testing user relationships',
    isActive: true
  }

  try {
    const response = await fetch(`${BASE_URL}/counties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(countyData),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ County created successfully!')
      console.log(`County ID: ${data.county.id}`)
      console.log(`County Name: ${data.county.name}`)
      testCountyId = data.county.id
      return true
    } else {
      console.log('❌ County creation failed:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ County creation error:', error.message)
    return false
  }
}

async function createUsersWithCounty() {
  console.log('\n👥 Creating users with county relationship...')
  
  if (!testCountyId) {
    console.log('❌ No test county available')
    return false
  }

  const users = [
    {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Alice Johnson',
      county: testCountyId
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Bob Smith',
      county: testCountyId
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Charlie Brown',
      county: testCountyId
    }
  ]

  for (const userData of users) {
    try {
      const response = await fetch(`${BASE_URL}/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      const data = await response.json()
      
      if (response.ok) {
        console.log(`✅ User created: ${userData.name} (ID: ${data.doc.id})`)
        testUserIds.push(data.doc.id)
      } else {
        console.log(`❌ User creation failed for ${userData.name}:`, data.message)
      }
    } catch (error) {
      console.error(`❌ User creation error for ${userData.name}:`, error.message)
    }
  }

  return testUserIds.length > 0
}

async function testGetUsersInCounty() {
  console.log('\n📋 Testing: Get users in county...')
  
  if (!testCountyId) {
    console.log('❌ No test county available')
    return false
  }

  try {
    const response = await fetch(`${BASE_URL}/counties/${testCountyId}/users`)
    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Successfully retrieved users in county!')
      console.log(`County: ${data.county.name} (${data.county.code})`)
      console.log(`Total users: ${data.totalUsers}`)
      console.log('Users:')
      data.users.forEach(user => {
        console.log(`  - ${user.name} (${user.email})`)
      })
      return true
    } else {
      console.log('❌ Failed to get users in county:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Get users in county error:', error.message)
    return false
  }
}

async function testGetUserWithCountyData() {
  console.log('\n👤 Testing: Get user with county data...')
  
  if (testUserIds.length === 0) {
    console.log('❌ No test users available')
    return false
  }

  const userId = testUserIds[0]

  try {
    const response = await fetch(`${BASE_URL}/users/${userId}?depth=1`)
    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Successfully retrieved user with county data!')
      console.log(`User: ${data.name} (${data.email})`)
      if (data.county) {
        console.log(`County: ${data.county.name} (${data.county.code})`)
        console.log(`County Coordinates: ${data.county.coordinates.latitude}, ${data.county.coordinates.longitude}`)
      } else {
        console.log('County: Not assigned')
      }
      return true
    } else {
      console.log('❌ Failed to get user with county data:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Get user with county data error:', error.message)
    return false
  }
}

async function testUpdateUserCounty() {
  console.log('\n✏️  Testing: Update user county...')
  
  if (testUserIds.length === 0 || !authToken) {
    console.log('❌ No test users or auth token available')
    return false
  }

  const userId = testUserIds[0]

  try {
    // First, create another county to move user to
    const newCountyResponse = await fetch(`${BASE_URL}/counties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        name: 'Another Test County',
        code: 'TEST-USERS-002',
        coordinates: { latitude: -2.0000, longitude: 37.0000 },
        description: 'Another test county'
      }),
    })

    const newCountyData = await newCountyResponse.json()
    
    if (!newCountyResponse.ok) {
      console.log('❌ Failed to create second county')
      return false
    }

    const newCountyId = newCountyData.county.id
    console.log(`Created second county: ${newCountyData.county.name} (ID: ${newCountyId})`)

    // Now update user's county
    const updateResponse = await fetch(`${BASE_URL}/users/${userId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        county: newCountyId
      }),
    })

    const updateData = await updateResponse.json()
    
    if (updateResponse.ok) {
      console.log('✅ Successfully updated user county!')
      console.log(`User moved to county ID: ${newCountyId}`)
      return true
    } else {
      console.log('❌ Failed to update user county:', updateData.error || updateData.message)
      return false
    }
  } catch (error) {
    console.error('❌ Update user county error:', error.message)
    return false
  }
}

async function cleanup() {
  console.log('\n🧹 Cleaning up test data...')
  
  if (!authToken) {
    console.log('❌ No auth token for cleanup')
    return
  }

  // Delete test users
  for (const userId of testUserIds) {
    try {
      await fetch(`${BASE_URL}/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })
      console.log(`✅ Deleted user ${userId}`)
    } catch (error) {
      console.log(`❌ Failed to delete user ${userId}`)
    }
  }

  // Delete test counties
  if (testCountyId) {
    try {
      await fetch(`${BASE_URL}/counties/${testCountyId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })
      console.log(`✅ Deleted county ${testCountyId}`)
    } catch (error) {
      console.log(`❌ Failed to delete county ${testCountyId}`)
    }
  }
}

async function runCountyUserTests() {
  console.log('🧪 Starting County-User Relationship Tests')
  console.log('=' .repeat(50))
  
  // Login
  const loginSuccess = await login()
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication')
    return
  }

  // Run tests
  await createTestCounty()
  await createUsersWithCounty()
  await testGetUsersInCounty()
  await testGetUserWithCountyData()
  await testUpdateUserCounty()
  
  // Cleanup
  await cleanup()
  
  console.log('\n' + '=' .repeat(50))
  console.log('🏁 County-User relationship tests completed!')
}

// Run the tests
runCountyUserTests().catch(console.error)
