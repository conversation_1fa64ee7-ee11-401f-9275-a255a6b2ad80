# Counties CRUD API - Quick Reference

## 🚀 **Quick Commands**

### Authentication (Required for Create/Update/Delete)
```bash
# Login to get JWT token
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your-password"}'

# Extract token from response and use in subsequent requests
export TOKEN="your-jwt-token-here"
```

### CREATE County
```bash
curl -X POST http://localhost:3000/api/counties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Nairobi",
    "code": "KE-047",
    "coordinates": {"latitude": -1.2921, "longitude": 36.8219},
    "description": "Kenya'\''s capital city",
    "isActive": true
  }'
```

### READ Counties (List)
```bash
# Get all counties
curl "http://localhost:3000/api/counties"

# With filters
curl "http://localhost:3000/api/counties?name=Nairobi&active=true&limit=10"

# Near coordinates
curl "http://localhost:3000/api/counties?near_lat=-1.2921&near_lng=36.8219&radius=50"
```

### READ Single County
```bash
# By ID
curl "http://localhost:3000/api/counties/1"

# By code (if supported)
curl "http://localhost:3000/api/counties/KE-047"
```

### UPDATE County
```bash
# Full update
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Updated Nairobi",
    "description": "Updated description",
    "coordinates": {"latitude": -1.3000, "longitude": 36.8000}
  }'

# Partial update (only name)
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name": "New Name Only"}'
```

### DELETE County
```bash
curl -X DELETE http://localhost:3000/api/counties/1 \
  -H "Authorization: Bearer $TOKEN"
```

## 📊 **Response Examples**

### Success Responses

**Create/Update Success:**
```json
{
  "message": "County created successfully",
  "county": {
    "id": "1",
    "name": "Nairobi",
    "code": "KE-047",
    "coordinates": {"latitude": -1.2921, "longitude": 36.8219},
    "description": "Kenya's capital city",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**List Counties:**
```json
{
  "counties": [...],
  "totalCounties": 47,
  "page": 1,
  "limit": 50,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPrevPage": false
}
```

**Delete Success:**
```json
{
  "message": "County deleted successfully",
  "id": 1
}
```

### Error Responses

**Validation Error (400):**
```json
{
  "error": "Missing required fields: name, code, coordinates (latitude, longitude)"
}
```

**Authentication Error (401):**
```json
{
  "error": "Authentication required"
}
```

**Not Found (404):**
```json
{
  "error": "County not found"
}
```

**Duplicate Code (409):**
```json
{
  "error": "County code already exists",
  "message": "A county with this code already exists"
}
```

## ✅ **Field Validation**

### Required Fields (CREATE):
- `name` - String, county name
- `code` - String, unique identifier
- `coordinates.latitude` - Number, -90 to 90
- `coordinates.longitude` - Number, -180 to 180

### Optional Fields:
- `description` - String, text description
- `isActive` - Boolean, default: true

### Validation Rules:
- County codes must be unique
- Latitude: -90 ≤ value ≤ 90
- Longitude: -180 ≤ value ≤ 180
- All coordinate values must be numbers

## 🔐 **Authentication Requirements**

| Operation | Auth Required | HTTP Method |
|-----------|---------------|-------------|
| Create    | ✅ Yes        | POST        |
| Read List | ❌ No         | GET         |
| Read One  | ❌ No         | GET         |
| Update    | ✅ Yes        | PUT         |
| Delete    | ✅ Yes        | DELETE      |

## 🧪 **Testing**

### Run Complete Test Suite:
```bash
node examples/test-counties-crud.js
```

### Manual Testing Sequence:
```bash
# 1. Login
TOKEN=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  | jq -r '.token')

# 2. Create
curl -X POST http://localhost:3000/api/counties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name":"Test","code":"TEST-001","coordinates":{"latitude":0,"longitude":0}}'

# 3. Read
curl "http://localhost:3000/api/counties"

# 4. Update
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name":"Updated Test"}'

# 5. Delete
curl -X DELETE http://localhost:3000/api/counties/1 \
  -H "Authorization: Bearer $TOKEN"
```

## 📋 **HTTP Status Codes**

- `200` - OK (successful read, update, delete)
- `201` - Created (successful create)
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `404` - Not Found (county doesn't exist)
- `409` - Conflict (duplicate county code)
- `500` - Internal Server Error

## 🔄 **Partial Updates**

The UPDATE endpoint supports partial updates. Only include fields you want to change:

```bash
# Update only name
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name": "New Name"}'

# Update only coordinates
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"coordinates": {"latitude": -2.0, "longitude": 37.0}}'

# Update only active status
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"isActive": false}'
```

## 🌐 **Frontend Integration**

### JavaScript/TypeScript Example:
```javascript
// Create county
const createCounty = async (countyData, token) => {
  const response = await fetch('/api/counties', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(countyData)
  })
  return response.json()
}

// Update county
const updateCounty = async (id, updateData, token) => {
  const response = await fetch(`/api/counties/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(updateData)
  })
  return response.json()
}

// Delete county
const deleteCounty = async (id, token) => {
  const response = await fetch(`/api/counties/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  return response.json()
}
```

Complete CRUD functionality is now available for the Counties collection! 🎉
