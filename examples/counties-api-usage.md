# Counties API Documentation

## 🗺️ **Counties Collection & API**

I've created a comprehensive counties collection with coordinates and multiple API endpoints for geographic data management.

## 📊 **Collection Features**

### Core Fields:
- **Name**: County name (required)
- **Code**: Unique county code (required)
- **Coordinates**: Latitude/longitude with validation (required)
- **Description**: Text description of the county
- **Is Active**: Boolean flag for active status
- **Timestamps**: Created and updated timestamps

### Validation:
- Latitude: -90 to 90 degrees
- Longitude: -180 to 180 degrees
- Unique county codes
- Required coordinates

## 🔗 **API Endpoints**

### 1. **Get All Counties** (with filtering)
```bash
GET /api/counties
```

**Query Parameters:**
- `name` - Search by county name (contains)
- `code` - Filter by exact county code
- `active` - Filter by active status (true/false)
- `near_lat` & `near_lng` - Find counties near coordinates
- `radius` - Radius in km for proximity search (default: 100km)
- `limit` - Items per page (default: 50)
- `page` - Page number (default: 1)
- `sort` - Sort field (default: name)

**Examples:**
```bash
# Get all counties
curl "http://localhost:3000/api/counties"

# Find counties near Nairobi (within 50km)
curl "http://localhost:3000/api/counties?near_lat=-1.2921&near_lng=36.8219&radius=50"

# Search for counties containing "Nairobi"
curl "http://localhost:3000/api/counties?name=Nairobi"

# Get active counties only
curl "http://localhost:3000/api/counties?active=true"

# Get county by code
curl "http://localhost:3000/api/counties?code=KE-047"
```

### 2. **Get Single County**
```bash
GET /api/counties/:id
```

**Examples:**
```bash
# Get county by ID
curl "http://localhost:3000/api/counties/1"

# Get county by code
curl "http://localhost:3000/api/counties/KE-047"
```

### 3. **Get Counties in Bounding Box**
```bash
GET /api/counties/bounds?north=1&south=-5&east=42&west=33
```

**Query Parameters:**
- `north` - Northern latitude boundary
- `south` - Southern latitude boundary  
- `east` - Eastern longitude boundary
- `west` - Western longitude boundary

**Example:**
```bash
# Get counties within Kenya's approximate bounds
curl "http://localhost:3000/api/counties/bounds?north=5.019&south=-4.678&east=41.899&west=33.908"
```

## 📝 **Response Format**

### Counties List Response:
```json
{
  "counties": [
    {
      "id": "1",
      "name": "Nairobi",
      "code": "KE-047",
      "coordinates": {
        "latitude": -1.2921,
        "longitude": 36.8219
      },
      "description": "Kenya's capital and largest city, serving as the country's economic and political hub.",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalCounties": 47,
  "page": 1,
  "limit": 50,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPrevPage": false
}
```

### Single County Response:
```json
{
  "county": {
    "id": "1",
    "name": "Nairobi",
    // ... full county object
  }
}
```

### Bounding Box Response:
```json
{
  "counties": [...],
  "totalCounties": 15,
  "bounds": {
    "north": 1,
    "south": -5,
    "east": 42,
    "west": 33
  }
}
```

## 🧪 **Sample Data Creation**

### Create a County via API:
```bash
curl -X POST http://localhost:3000/api/counties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Nairobi",
    "code": "KE-047",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    },
    "description": "Kenya'\''s capital and largest city, serving as the country'\''s economic and political hub.",
    "isActive": true
  }'
```

## 🗺️ **Frontend Integration Examples**

### React Hook for Counties:
```typescript
import { useState, useEffect } from 'react'

interface County {
  id: string
  name: string
  coordinates: { latitude: number; longitude: number }
  // ... other fields
}

export function useCounties(filters?: Record<string, string>) {
  const [counties, setCounties] = useState<County[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchCounties = async () => {
      setLoading(true)
      const params = new URLSearchParams(filters)
      const response = await fetch(`/api/counties?${params}`)
      const data = await response.json()
      setCounties(data.counties)
      setLoading(false)
    }

    fetchCounties()
  }, [filters])

  return { counties, loading }
}
```

### Map Integration:
```typescript
// Find counties near user location
const findNearbyCounties = async (lat: number, lng: number, radius = 50) => {
  const response = await fetch(
    `/api/counties?near_lat=${lat}&near_lng=${lng}&radius=${radius}`
  )
  return response.json()
}

// Get counties in map viewport
const getCountiesInBounds = async (bounds: {
  north: number, south: number, east: number, west: number
}) => {
  const { north, south, east, west } = bounds
  const response = await fetch(
    `/api/counties/bounds?north=${north}&south=${south}&east=${east}&west=${west}`
  )
  return response.json()
}
```

## 🔧 **Admin Panel Access**

Counties can be managed through the Payload admin panel at:
- **List View**: `/admin/collections/counties`
- **Create New**: `/admin/collections/counties/create`
- **Edit County**: `/admin/collections/counties/:id`

## 🌍 **Use Cases**

1. **Location-based Services**: Find services near user location
2. **Geographic Analytics**: Population, economic data analysis
3. **Map Applications**: Display county boundaries and info
4. **Government Portals**: County information systems
5. **Tourism Apps**: Regional information and attractions
6. **Business Intelligence**: Market analysis by region

The counties API provides comprehensive geographic data management with powerful querying capabilities for location-based applications!
