// Test script for County-User relationship using filters
// Run with: node examples/test-county-user-filters.js

const BASE_URL = 'http://localhost:3000/api'

// Test credentials
const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = 'password123'

let authToken = null
let testCountyId = null
let testUserIds = []

async function login() {
  console.log('🔐 Logging in as admin...')
  
  try {
    const response = await fetch(`${BASE_URL}/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
      }),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Login successful!')
      authToken = data.token
      return true
    } else {
      console.log('❌ Login failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Login error:', error.message)
    return false
  }
}

async function createTestData() {
  console.log('\n🏗️  Creating test data...')
  
  if (!authToken) {
    console.log('❌ No auth token available')
    return false
  }

  // Create test county
  try {
    const countyResponse = await fetch(`${BASE_URL}/counties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        name: 'Filter Test County',
        code: 'FILTER-TEST-001',
        coordinates: { latitude: -1.0000, longitude: 36.0000 },
        description: 'A test county for filter testing',
        isActive: true
      }),
    })

    const countyData = await countyResponse.json()
    
    if (countyResponse.ok) {
      testCountyId = countyData.county.id
      console.log(`✅ Created test county: ${countyData.county.name} (ID: ${testCountyId})`)
    } else {
      console.log('❌ Failed to create county:', countyData.error)
      return false
    }
  } catch (error) {
    console.error('❌ County creation error:', error.message)
    return false
  }

  // Create test users
  const users = [
    { email: '<EMAIL>', name: 'Filter User 1', county: testCountyId },
    { email: '<EMAIL>', name: 'Filter User 2', county: testCountyId },
    { email: '<EMAIL>', name: 'Filter User 3', county: testCountyId },
    { email: '<EMAIL>', name: 'No County User', county: null }
  ]

  for (const userData of users) {
    try {
      const userResponse = await fetch(`${BASE_URL}/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...userData,
          password: 'password123'
        }),
      })

      const userResult = await userResponse.json()
      
      if (userResponse.ok) {
        testUserIds.push(userResult.doc.id)
        console.log(`✅ Created user: ${userData.name} (ID: ${userResult.doc.id})`)
      } else {
        console.log(`❌ Failed to create user ${userData.name}:`, userResult.message)
      }
    } catch (error) {
      console.error(`❌ User creation error for ${userData.name}:`, error.message)
    }
  }

  return testUserIds.length > 0
}

async function testFilterByCountyId() {
  console.log('\n🔍 Test 1: Filter users by county ID')
  
  try {
    const response = await fetch(`${BASE_URL}/users?where[county][equals]=${testCountyId}`)
    const data = await response.json()
    
    if (response.ok) {
      console.log(`✅ Found ${data.totalDocs} users in county ${testCountyId}`)
      data.docs.forEach(user => {
        console.log(`  - ${user.name} (${user.email})`)
      })
      return true
    } else {
      console.log('❌ Filter by county ID failed:', data.error)
      return false
    }
  } catch (error) {
    console.error('❌ Filter by county ID error:', error.message)
    return false
  }
}

async function testFilterByCountyIdWithDepth() {
  console.log('\n🔍 Test 2: Filter users by county ID with county data')
  
  try {
    const response = await fetch(`${BASE_URL}/users?where[county][equals]=${testCountyId}&depth=1`)
    const data = await response.json()
    
    if (response.ok) {
      console.log(`✅ Found ${data.totalDocs} users with county data`)
      data.docs.forEach(user => {
        console.log(`  - ${user.name}: County ${user.county?.name} (${user.county?.code})`)
      })
      return true
    } else {
      console.log('❌ Filter with depth failed:', data.error)
      return false
    }
  } catch (error) {
    console.error('❌ Filter with depth error:', error.message)
    return false
  }
}

async function testFilterUsersWithoutCounty() {
  console.log('\n🔍 Test 3: Filter users without county')
  
  try {
    const response = await fetch(`${BASE_URL}/users?where[county][exists]=false`)
    const data = await response.json()
    
    if (response.ok) {
      console.log(`✅ Found ${data.totalDocs} users without county`)
      data.docs.forEach(user => {
        console.log(`  - ${user.name} (${user.email})`)
      })
      return true
    } else {
      console.log('❌ Filter users without county failed:', data.error)
      return false
    }
  } catch (error) {
    console.error('❌ Filter users without county error:', error.message)
    return false
  }
}

async function testFilterByCountyName() {
  console.log('\n🔍 Test 4: Filter users by county name')
  
  try {
    const response = await fetch(`${BASE_URL}/users?where[county.name][equals]=Filter Test County&depth=1`)
    const data = await response.json()
    
    if (response.ok) {
      console.log(`✅ Found ${data.totalDocs} users in "Filter Test County"`)
      data.docs.forEach(user => {
        console.log(`  - ${user.name}: ${user.county?.name}`)
      })
      return true
    } else {
      console.log('❌ Filter by county name failed:', data.error)
      return false
    }
  } catch (error) {
    console.error('❌ Filter by county name error:', error.message)
    return false
  }
}

async function testCombinedFilters() {
  console.log('\n🔍 Test 5: Combined filters (county + name contains)')
  
  try {
    const response = await fetch(`${BASE_URL}/users?where[county][equals]=${testCountyId}&where[name][contains]=Filter&depth=1`)
    const data = await response.json()
    
    if (response.ok) {
      console.log(`✅ Found ${data.totalDocs} users in county with "Filter" in name`)
      data.docs.forEach(user => {
        console.log(`  - ${user.name}: ${user.county?.name}`)
      })
      return true
    } else {
      console.log('❌ Combined filters failed:', data.error)
      return false
    }
  } catch (error) {
    console.error('❌ Combined filters error:', error.message)
    return false
  }
}

async function cleanup() {
  console.log('\n🧹 Cleaning up test data...')
  
  if (!authToken) {
    console.log('❌ No auth token for cleanup')
    return
  }

  // Delete test users
  for (const userId of testUserIds) {
    try {
      await fetch(`${BASE_URL}/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })
      console.log(`✅ Deleted user ${userId}`)
    } catch (error) {
      console.log(`❌ Failed to delete user ${userId}`)
    }
  }

  // Delete test county
  if (testCountyId) {
    try {
      await fetch(`${BASE_URL}/counties/${testCountyId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })
      console.log(`✅ Deleted county ${testCountyId}`)
    } catch (error) {
      console.log(`❌ Failed to delete county ${testCountyId}`)
    }
  }
}

async function runFilterTests() {
  console.log('🧪 Starting County-User Filter Tests')
  console.log('=' .repeat(50))
  
  // Login
  const loginSuccess = await login()
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication')
    return
  }

  // Create test data
  const dataCreated = await createTestData()
  if (!dataCreated) {
    console.log('❌ Cannot proceed without test data')
    return
  }

  // Run filter tests
  await testFilterByCountyId()
  await testFilterByCountyIdWithDepth()
  await testFilterUsersWithoutCounty()
  await testFilterByCountyName()
  await testCombinedFilters()
  
  // Cleanup
  await cleanup()
  
  console.log('\n' + '=' .repeat(50))
  console.log('🏁 County-User filter tests completed!')
}

// Run the tests
runFilterTests().catch(console.error)
