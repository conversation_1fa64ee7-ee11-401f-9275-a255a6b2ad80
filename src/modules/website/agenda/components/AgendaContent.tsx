import { Card } from '@/components/ui/card'

interface Speaker {
  name: string
  title: string
  company: string
  image: string
}

interface AgendaItem {
  time: string
  title: string
  description?: string
  speakers?: Speaker[]
  type?: 'session' | 'break' | 'networking'
  color?: string
}

interface DaySchedule {
  day: string
  date: string
  label: string
  items: AgendaItem[]
}

interface AgendaContentProps {
  activeDay: string
}

export default function AgendaContent({ activeDay }: AgendaContentProps) {
  const schedule: DaySchedule[] = [
    {
      day: 'Day 1',
      date: 'MAY 26, 2024',
      label: 'Opening Day',
      items: [
        {
          time: '9:00 AM - 10:30 AM',
          title: 'Registration & Networking',
          description:
            'Welcome reception and participant registration. Network with fellow attendees and grab your conference materials.',
          type: 'networking',
          color: 'bg-blue-100',
        },
        {
          time: '10:30 AM - 11:00 AM',
          title: 'Coffee Break',
          type: 'break',
          color: 'bg-gray-100',
        },
        {
          time: '11:00 AM - 11:30 AM',
          title: 'Opening Remarks',
          description: 'Welcome address and conference overview from our distinguished speakers.',
          speakers: [
            {
              name: '<PERSON>',
              title: 'Chief Executive Officer',
              company: 'Tech Innovations Inc.',
              image: '/placeholder.svg?height=80&width=80',
            },
            {
              name: '<PERSON>',
              title: 'Director of Operations',
              company: 'Global Solutions Ltd.',
              image: '/placeholder.svg?height=80&width=80',
            },
          ],
          color: 'bg-green-100',
        },
        {
          time: '11:30 AM - 12:30 PM',
          title: 'Keynote: The Future of Innovation',
          description:
            'An inspiring keynote on emerging technologies and their impact on business transformation.',
          speakers: [
            {
              name: 'Dr. Sarah Chen',
              title: 'Chief Innovation Officer',
              company: 'Future Tech Labs',
              image: '/placeholder.svg?height=80&width=80',
            },
          ],
          color: 'bg-purple-100',
        },
        {
          time: '12:30 PM - 2:00 PM',
          title: 'Networking Lunch',
          type: 'break',
          color: 'bg-yellow-100',
        },
      ],
    },
    {
      day: 'Day 2',
      date: 'MAY 27, 2024',
      label: 'Innovation Summit',
      items: [
        {
          time: '9:00 AM - 10:30 AM',
          title: 'Summit Overview & Strategic Planning',
          description:
            'Comprehensive overview of summit objectives and strategic planning methodologies.',
          speakers: [
            {
              name: 'Michael Goldsmith',
              title: 'Senior Vice President',
              company: 'Innovation Partners',
              image: '/placeholder.svg?height=80&width=80',
            },
          ],
          color: 'bg-purple-100',
        },
        {
          time: '10:30 AM - 11:00 AM',
          title: 'Coffee Break',
          type: 'break',
          color: 'bg-gray-100',
        },
        {
          time: '11:00 AM - 12:30 PM',
          title: 'Youth and The Future of Work',
          description:
            'Exploring how young professionals are shaping the future workplace and driving innovation across industries. This panel will discuss emerging trends and opportunities.',
          speakers: [
            {
              name: 'Sarah Johnson',
              title: 'Youth Development Director',
              company: 'Future Leaders Foundation',
              image: '/placeholder.svg?height=80&width=80',
            },
            {
              name: 'Bill Martin',
              title: 'Innovation Strategist',
              company: 'NextGen Solutions',
              image: '/placeholder.svg?height=80&width=80',
            },
          ],
          color: 'bg-orange-100',
        },
        {
          time: '12:30 PM - 2:00 PM',
          title: 'Networking Lunch & Exhibition',
          type: 'break',
          color: 'bg-yellow-100',
        },
      ],
    },
    {
      day: 'Day 3',
      date: 'MAY 28, 2024',
      label: 'Panel Discussions',
      items: [
        {
          time: '9:00 AM - 10:30 AM',
          title: 'Youth Entrepreneurship in the Digital Age',
          description:
            'Panel Discussion: Youth Entrepreneurship in the Age of AI & IoT Economy. The forum shall culminate May 2020. Our robust African entrepreneurs are expected to participate in the forum. The forum will bring together over 1000 participants from across the continent including 200 youth and women. What are the distinct challenges for young entrepreneurs in emerging markets? How can AI help? What opportunities and platforms are there for the youth?',
          speakers: [
            {
              name: 'Pauline Wanjiku',
              title: 'Founder & CEO',
              company: 'Tech Startup Hub',
              image: '/placeholder.svg?height=60&width=60',
            },
            {
              name: 'John Ochieng',
              title: 'AI Research Director',
              company: 'Innovation Labs',
              image: '/placeholder.svg?height=60&width=60',
            },
            {
              name: 'Dr. David Gachuki',
              title: 'Senior Economist',
              company: 'Economic Development Bank',
              image: '/placeholder.svg?height=60&width=60',
            },
            {
              name: 'Faith Kiprop',
              title: 'Investment Manager',
              company: 'Venture Capital Partners',
              image: '/placeholder.svg?height=60&width=60',
            },
            {
              name: 'Charles Mwai',
              title: 'Innovation Consultant',
              company: 'Strategic Advisors',
              image: '/placeholder.svg?height=60&width=60',
            },
          ],
          color: 'bg-blue-100',
        },
        {
          time: '10:30 AM - 11:00 AM',
          title: 'Coffee Break',
          type: 'break',
          color: 'bg-gray-100',
        },
        {
          time: '11:00 AM - 12:30 PM',
          title: 'Technology & Sustainability Panel',
          description:
            'Discussing the intersection of technology and sustainable development goals.',
          speakers: [
            {
              name: 'Dr. Maria Santos',
              title: 'Sustainability Director',
              company: 'Green Tech Solutions',
              image: '/placeholder.svg?height=80&width=80',
            },
            {
              name: 'James Wilson',
              title: 'Environmental Strategist',
              company: 'EcoInnovate',
              image: '/placeholder.svg?height=80&width=80',
            },
          ],
          color: 'bg-green-100',
        },
        {
          time: '12:30 PM - 2:00 PM',
          title: 'Networking Lunch',
          type: 'break',
          color: 'bg-yellow-100',
        },
      ],
    },
  ]

  // Find the current day's schedule
  const currentDaySchedule = schedule.find((day) => day.day === activeDay) || schedule[0]

  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-5xl mx-auto px-4">
        {/* Day Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">AGENDA</h2>
          <div className="bg-white rounded-lg shadow-sm border inline-block px-8 py-4">
            <div className="font-bold text-xl text-gray-900">{currentDaySchedule.date}</div>
            <div className="text-blue-600 font-medium">{currentDaySchedule.label}</div>
          </div>
        </div>

        {/* Day Schedule */}
        <div className="space-y-6">
          {currentDaySchedule.items.map((item, itemIndex) => (
            <Card
              key={itemIndex}
              className="p-6 bg-white shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="flex gap-6">
                {/* Time Block */}
                <div className="flex-shrink-0">
                  <div
                    className={`px-4 py-2 rounded-lg text-sm font-medium ${item.color || 'bg-gray-100'}`}
                  >
                    {item.time}
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1">
                  {/* Session Title */}
                  <h3 className="font-bold text-xl text-gray-900 mb-3">{item.title}</h3>

                  {/* Description */}
                  {item.description && (
                    <p className="text-gray-600 mb-4 leading-relaxed">{item.description}</p>
                  )}

                  {/* Speakers */}
                  {item.speakers && item.speakers.length > 0 && (
                    <div className="space-y-4">
                      {item.speakers.length <= 2 ? (
                        // Large speaker cards for 1-2 speakers
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {item.speakers.map((speaker, speakerIndex) => (
                            <div
                              key={speakerIndex}
                              className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg"
                            >
                              <img
                                src={speaker.image || '/placeholder.svg'}
                                alt={speaker.name}
                                className="w-16 h-16 rounded-full object-cover"
                              />
                              <div>
                                <div className="font-semibold text-gray-900">{speaker.name}</div>
                                <div className="text-sm text-gray-600">{speaker.title}</div>
                                <div className="text-sm text-gray-500">{speaker.company}</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        // Panel layout for 3+ speakers
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="text-sm font-medium text-gray-700 mb-3">
                            Panel Speakers:
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                            {item.speakers.map((speaker, speakerIndex) => (
                              <div key={speakerIndex} className="text-center">
                                <img
                                  src={speaker.image || '/placeholder.svg'}
                                  alt={speaker.name}
                                  className="w-12 h-12 rounded-full object-cover mx-auto mb-2"
                                />
                                <div className="text-xs font-medium text-gray-900">
                                  {speaker.name}
                                </div>
                                <div className="text-xs text-gray-600">{speaker.title}</div>
                                <div className="text-xs text-gray-500">{speaker.company}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
