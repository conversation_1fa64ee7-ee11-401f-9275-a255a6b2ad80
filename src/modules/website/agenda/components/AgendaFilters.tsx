'use client'

interface AgendaFiltersProps {
  activeDay: string
  setActiveDay: (day: string) => void
}

export default function AgendaFilters({ activeDay, setActiveDay }: AgendaFiltersProps) {
  const days = ['Day 1', 'Day 2', 'Day 3']

  return (
    <div className="bg-white py-12">
      <div className="max-w-7xl mx-auto px-4">
        {/* Filter Options */}
        <div className="flex items-center justify-center gap-12 mb-12">
          <span className="text-gray-600 font-medium">Filter by:</span>
          <div className="flex items-center gap-12">
            <button className="text-gray-700 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-300 pb-2 transition-all">
              Speaker
            </button>
            <div className="w-px h-6 bg-gray-300"></div>
            <button className="text-gray-700 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-300 pb-2 transition-all">
              Thematic Area
            </button>
            <div className="w-px h-6 bg-gray-300"></div>
            <button className="text-gray-700 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-300 pb-2 transition-all">
              Topic
            </button>
          </div>
        </div>

        {/* Day Tabs */}
        <div className="flex justify-center border-b">
          {days.map((day) => (
            <button
              key={day}
              onClick={() => setActiveDay(day)}
              className={`px-12 py-4 font-medium border-b-2 transition-colors ${
                activeDay === day
                  ? 'border-black text-black bg-gray-50'
                  : 'border-transparent text-gray-600 hover:text-gray-900'
              }`}
            >
              {day}
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}
