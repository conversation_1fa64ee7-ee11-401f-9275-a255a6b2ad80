import { Users, FileText, TrendingUp, Map } from 'lucide-react'
import Image from 'next/image'

const stats = [
  {
    number: '13+',
    label: 'Counties Involved',
    subtitle: 'Across Kenya',
    icon: Map,
  },
  {
    number: '40+',
    label: 'IKIA Assets',
    subtitle: 'Fully Documented',
    icon: FileText,
  },
  {
    number: '1000+',
    label: 'Expected Attendees',
    subtitle: 'Local + Global',
    icon: Users,
  },
  {
    number: '25+',
    label: 'Investment Opportunities',
    subtitle: 'Across 5 Sectors',
    icon: TrendingUp,
  },
]

// Iconography categories representing thematic areas
const categories = [
  {
    name: 'Traditional Medicine',
    image: '/assets/iconography/traditional-med-no-bg.png',
  },
  {
    name: 'Cultural Heritage',
    image: '/assets/iconography/heritage-no-bg.png',
  },
  {
    name: 'Indigenous Innovation',
    image: '/assets/iconography/indiginious-tech-no-bg.png',
  },
  {
    name: 'Heritage Sites',
    image: '/assets/iconography/heritage-sites-no-bg.png',
  },
  {
    name: 'Cultural Arts & Music',
    image: '/assets/iconography/music-and-song-no-bg.png',
  },
  {
    name: 'Knowledge Systems',
    image: '/assets/iconography/ikms-inkibank-no-bg.png',
  },
]

export default function StatsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Category Icons */}
        <div className="flex justify-between items-center mb-20 px-4 sm:px-8 md:px-12 lg:px-16">
          {categories.map((category, index) => (
            <div
              key={index}
              className="w-32 h-32 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-36 lg:h-36  rounded-none flex items-center justify-center transition-all duration-300 cursor-pointer group"
              title={category.name}
            >
              <div className="relative w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 group-hover:scale-110 transition-transform duration-300">
                <Image
                  src={category.image}
                  alt={category.name}
                  fill
                  className="object-contain"
                />
              </div>
            </div>
          ))}
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow"
            >
              <div className="mb-4 flex justify-center">
                <stat.icon className="w-12 h-12 text-[#7E2518]" />
              </div>
              <div className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-2">
                {stat.number}
              </div>
              <div className="text-lg font-semibold text-gray-800 mb-1">{stat.label}</div>
              <div className="text-sm text-gray-600">{stat.subtitle}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
