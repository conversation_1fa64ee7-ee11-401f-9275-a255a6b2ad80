'use client'

import { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ExternalLink } from 'lucide-react'
import FilterSection from './FilterSection'
import { thematicAreas } from '@/lib/registration-data'

const counties = ['MACHAKOS', 'MURANGA', 'TURKANA', 'KAKAMEGA', 'MOMBASA']

const assets = {
  MACHAKOS: [
    {
      id: 1,
      name: 'Machakos Wild Herbal Tea Blend',
      description: 'Traditional tea blend used for digestive health',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Digestive Health',
      thematicArea: 'Traditional Medicine',
      county: 'MACHAKOS',
    },
    {
      id: 2,
      name: 'Kamba Drought-Resistant Crops',
      description: 'Indigenous crop varieties adapted to arid conditions',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Agricultural Systems',
      thematicArea: 'Agricultural Practices',
      county: 'MACHAKOS',
    },
  ],
  MURANGA: [
    {
      id: 3,
      name: 'Kikuyu Coffee Processing',
      description: 'Traditional coffee processing methods',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Food Processing',
      thematicArea: 'Agricultural Practices',
      county: 'MURANGA',
    },
  ],
  TURKANA: [
    {
      id: 4,
      name: 'Pastoralist Water Management',
      description: 'Indigenous water harvesting and management',
      tags: ['Traditional Medicine', 'Water Systems'],
      category: 'Water Management',
      thematicArea: 'Environmental Conservation',
      county: 'TURKANA',
    },
  ],
  KAKAMEGA: [
    {
      id: 5,
      name: 'Luhya Medicinal Plants',
      description: 'Forest-based traditional medicine knowledge',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Medicinal Plants',
      thematicArea: 'Traditional Medicine',
      county: 'KAKAMEGA',
    },
  ],
  MOMBASA: [
    {
      id: 6,
      name: 'Coastal Salt Production',
      description: 'Traditional sea salt harvesting methods',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Food Systems',
      thematicArea: 'Traditional Knowledge Systems',
      county: 'MOMBASA',
    },
  ],
}

export default function AssetsSection() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCountyFilter, setSelectedCountyFilter] = useState('all')
  const [selectedThematicArea, setSelectedThematicArea] = useState('all')

  // Flatten all assets for filtering
  const allAssets = useMemo(() => {
    return Object.values(assets).flat()
  }, [])

  // Filter assets based on search and filters
  const filteredAssets = useMemo(() => {
    return allAssets.filter((asset) => {
      const matchesSearch =
        asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

      const matchesCounty = selectedCountyFilter === 'all' || asset.county === selectedCountyFilter
      const matchesThematicArea = selectedThematicArea === 'all' || asset.thematicArea === selectedThematicArea

      return matchesSearch && matchesCounty && matchesThematicArea
    })
  }, [allAssets, searchTerm, selectedCountyFilter, selectedThematicArea])

  // Get active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (searchTerm) count++
    if (selectedCountyFilter !== 'all') count++
    if (selectedThematicArea !== 'all') count++
    return count
  }, [searchTerm, selectedCountyFilter, selectedThematicArea])

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCountyFilter('all')
    setSelectedThematicArea('all')
  }

  // Determine which assets to show
  const displayAssets = activeFiltersCount > 0
    ? filteredAssets
    : allAssets

  return (
    <section className="py-16 bg-white" id="exhibitions-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4">
            EXPLORE ALL IKIA ASSETS
            <br />
            BY COUNTIES
          </h2>
        </div>

        {/* Filter Section */}
        <FilterSection
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectedCounty={selectedCountyFilter}
          onCountyChange={setSelectedCountyFilter}
          selectedThematicArea={selectedThematicArea}
          onThematicAreaChange={setSelectedThematicArea}
          counties={counties}
          thematicAreas={thematicAreas}
          onClearFilters={clearFilters}
          activeFiltersCount={activeFiltersCount}
        />


        {/* Results Summary */}
        {activeFiltersCount > 0 && (
          <div className="text-center mb-6">
            <p className="text-gray-600">
              Showing {displayAssets.length} asset{displayAssets.length !== 1 ? 's' : ''}
              {searchTerm && ` matching "${searchTerm}"`}
            </p>
          </div>
        )}

        {/* Asset Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayAssets.length > 0 ? (
            displayAssets.map((asset) => (
              <Card key={asset.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="h-32 sm:h-36 bg-gray-200 flex items-center justify-center overflow-hidden">
                  <span className="text-gray-500">Asset Image</span>
                </div>
                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-[#7E2518] mb-2">{asset.name}</h3>
                    <p className="text-gray-600 mb-3">{asset.description}</p>
                    {activeFiltersCount > 0 && (
                      <p className="text-sm text-gray-500 mb-2">
                        <span className="font-medium">County:</span> {asset.county} |
                        <span className="font-medium"> Theme:</span> {asset.thematicArea}
                      </p>
                    )}
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {asset.tags.map((tag, tagIndex) => (
                      <Badge
                        key={tagIndex}
                        variant="secondary"
                        className="bg-[#E8B32C] text-[#7E2518]"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge className="bg-[#159147] text-white">{asset.category}</Badge>
                    <Button size="sm" className="bg-[#7E2518] hover:bg-[#6B1F14]">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 text-lg">No assets found matching your criteria.</p>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="mt-4 border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white"
              >
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
