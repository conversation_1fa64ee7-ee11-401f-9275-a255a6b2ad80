'use client'

import { useState } from 'react'
import InteractiveMap from './KenyanMap'
import { Button } from '@/components/ui/button'
import { CountyCarousel } from '@/modules/website/exhibitions/components'
import Link from 'next/link'

interface CountyInfo {
  name: string
  code: string
  population?: string
  area?: string
}

export default function HeroSection() {
  const [selectedCounty, setSelectedCounty] = useState<CountyInfo | null>(null)

  const handleCountySelect = (county: CountyInfo) => {
    setSelectedCounty(county)
  }

  const handleBackToDefault = () => {
    setSelectedCounty(null)
  }
  return (
    <section className="bg-gradient-to-b from-gray-50 to-white py-12 lg:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Map */}
          <div className="order-1 lg:order-1">
            <InteractiveMap onCountySelect={handleCountySelect} selectedCounty={selectedCounty} />
          </div>

          {/* Content */}
          <div className="order-2 lg:order-2 space-y-8">
            {!selectedCounty ? (
              <>
                <div>
                  <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] leading-tight">
                    EXPLORE KENYA&apos;S
                    <br />
                    <span className="text-[#159147]">IKIA MAPS</span>
                  </h1>
                </div>

                <div className="space-y-4 md:space-x-4">
                  <Link href="/exhibitions#exhibitors-section">
                    <Button className="w-full lg:w-auto bg-[#7E2518] hover:bg-[#6B1F14] text-white px-8 py-3 text-lg">
                      GO TO EXHIBITORS
                    </Button>
                  </Link>

                  <Link href="/exhibitions#exhibitions-section">
                    <Button
                      variant="outline"
                      className="w-full lg:w-auto border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white px-8 py-3 text-lg"
                    >
                      GO TO EXHIBITIONS
                    </Button>
                  </Link>
                </div>
              </>
            ) : (
              // County Carousel
              <CountyCarousel county={selectedCounty} onBack={handleBackToDefault} />
            )}
          </div>
        </div>
      </div>
    </section>
  )
}
