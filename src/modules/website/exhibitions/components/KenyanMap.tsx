'use client'

import { useCallback, useState } from 'react'
import { ComposableMap, Geographies, Geography } from 'react-simple-maps'

interface CountyInfo {
  name: string
  code: string
  population?: string
  area?: string
}

interface InteractiveMapProps {
  onCountySelect: (county: CountyInfo) => void
  selectedCounty: CountyInfo | null
}

interface TooltipState {
  content: string
  x: number
  y: number
  visible: boolean
}

// Mock represented counties - these would come from API
const representedCounties = [
  'NAIROBI',
  'KIAMBU',
  'NAKURU',
  'MOMBASA',
  'KISUMU',
  'MERU',
  'KAKAMEGA',
  'MACHAKOS',
  'KILIFI',
  'BUNGOMA',
  'NYERI',
  'KERICHO',
  'UASIN GISHU',
]

const countyInfo: { [key: string]: CountyInfo } = {
  MOMBASA: { name: 'Mombasa', code: '001', population: '1,208,333', area: '229.7 km²' },
  KWALE: { name: '<PERSON><PERSON><PERSON>', code: '002', population: '866,820', area: '8,270.3 km²' },
  KILIFI: { name: '<PERSON><PERSON><PERSON>', code: '003', population: '1,453,787', area: '12,245.9 km²' },
  'TANA RIVER': { name: 'Tana River', code: '004', population: '315,943', area: '35,375.8 km²' },
  LAMU: { name: 'Lamu', code: '005', population: '143,920', area: '6,497.7 km²' },
  'TAITA TAVETA': {
    name: 'Taita Taveta',
    code: '006',
    population: '340,671',
    area: '17,083.9 km²',
  },
  GARISSA: { name: 'Garissa', code: '007', population: '841,353', area: '45,720.2 km²' },
  WAJIR: { name: 'Wajir', code: '008', population: '781,263', area: '55,840.6 km²' },
  MANDERA: { name: 'Mandera', code: '009', population: '1,025,756', area: '25,797.7 km²' },
  MARSABIT: { name: 'Marsabit', code: '010', population: '459,785', area: '66,923.1 km²' },
  ISIOLO: { name: 'Isiolo', code: '011', population: '268,002', area: '25,336.1 km²' },
  MERU: { name: 'Meru', code: '012', population: '1,545,714', area: '6,930.1 km²' },
  'THARAKA NITHI': {
    name: 'Tharaka Nithi',
    code: '013',
    population: '393,177',
    area: '2,609.5 km²',
  },
  EMBU: { name: 'Embu', code: '014', population: '608,599', area: '2,555.9 km²' },
  KITUI: { name: 'Kitui', code: '015', population: '1,136,187', area: '24,385.1 km²' },
  MACHAKOS: { name: 'Machakos', code: '016', population: '1,421,932', area: '5,952.9 km²' },
  MAKUENI: { name: 'Makueni', code: '017', population: '987,653', area: '8,008.9 km²' },
  NYANDARUA: { name: 'Nyandarua', code: '018', population: '638,289', area: '3,304.5 km²' },
  NYERI: { name: 'Nyeri', code: '019', population: '759,164', area: '2,361.0 km²' },
  KIRINYAGA: { name: 'Kirinyaga', code: '020', population: '610,411', area: '1,205.4 km²' },
  "MURANG'A": { name: "Murang'a", code: '021', population: '1,056,640', area: '2,325.8 km²' },
  KIAMBU: { name: 'Kiambu', code: '022', population: '2,417,735', area: '2,449.2 km²' },
  TURKANA: { name: 'Turkana', code: '023', population: '926,976', area: '68,680.3 km²' },
  'WEST POKOT': { name: 'West Pokot', code: '024', population: '621,241', area: '9,169.4 km²' },
  SAMBURU: { name: 'Samburu', code: '025', population: '310,327', area: '20,182.5 km²' },
  'TRANS NZOIA': { name: 'Trans Nzoia', code: '026', population: '990,341', area: '2,469.9 km²' },
  'UASIN GISHU': { name: 'Uasin Gishu', code: '027', population: '1,163,186', area: '3,345.2 km²' },
  'ELGEYO MARAKWET': {
    name: 'Elgeyo Marakwet',
    code: '028',
    population: '454,480',
    area: '3,049.2 km²',
  },
  NANDI: { name: 'Nandi', code: '029', population: '885,711', area: '2,884.4 km²' },
  BARINGO: { name: 'Baringo', code: '030', population: '666,763', area: '11,015.3 km²' },
  LAIKIPIA: { name: 'Laikipia', code: '031', population: '518,560', area: '9,229.5 km²' },
  NAKURU: { name: 'Nakuru', code: '032', population: '2,162,202', area: '7,495.1 km²' },
  NAROK: { name: 'Narok', code: '033', population: '1,157,873', area: '17,944.1 km²' },
  KAJIADO: { name: 'Kajiado', code: '034', population: '1,117,840', area: '21,292.7 km²' },
  KERICHO: { name: 'Kericho', code: '035', population: '901,777', area: '2,454.5 km²' },
  BOMET: { name: 'Bomet', code: '036', population: '875,689', area: '1,997.9 km²' },
  KAKAMEGA: { name: 'Kakamega', code: '037', population: '1,867,579', area: '3,033.8 km²' },
  VIHIGA: { name: 'Vihiga', code: '038', population: '590,013', area: '531.5 km²' },
  BUNGOMA: { name: 'Bungoma', code: '039', population: '1,670,570', area: '2,206.9 km²' },
  BUSIA: { name: 'Busia', code: '040', population: '893,681', area: '1,628.4 km²' },
  SIAYA: { name: 'Siaya', code: '041', population: '993,183', area: '2,496.1 km²' },
  KISUMU: { name: 'Kisumu', code: '042', population: '1,155,574', area: '2,009.5 km²' },
  'HOMA BAY': { name: 'Homa Bay', code: '043', population: '1,131,950', area: '3,154.7 km²' },
  MIGORI: { name: 'Migori', code: '044', population: '1,116,436', area: '2,586.4 km²' },
  KISII: { name: 'Kisii', code: '045', population: '1,266,860', area: '1,318.0 km²' },
  NYAMIRA: { name: 'Nyamira', code: '046', population: '605,576', area: '899.5 km²' },
  NAIROBI: { name: 'Nairobi', code: '047', population: '4,397,073', area: '696.1 km²' },
}

export default function InteractiveMap({ onCountySelect, selectedCounty }: InteractiveMapProps) {
  const [tooltip, setTooltip] = useState<TooltipState>({
    content: '',
    x: 0,
    y: 0,
    visible: false,
  })

  const kenyaCountiesUrl =
    'https://raw.githubusercontent.com/mikelmaron/kenya-election-data/master/data/counties.geojson'

  const handleCountyClick = useCallback(
    (geography: { properties: Record<string, string | number> }) => {
      const rawCountyName =
        geography.properties.COUNTY ||
        geography.properties.NAME ||
        geography.properties.COUNTY_NAM ||
        geography.properties.name ||
        geography.properties.county ||
        'Unknown'

      const countyName = rawCountyName.toString().toUpperCase().trim()

      // Handle special cases and variations
      let normalizedName = countyName
      if (countyName.includes('MURANG')) normalizedName = "MURANG'A"
      if (countyName.includes('TAITA') && countyName.includes('TAVETA'))
        normalizedName = 'TAITA TAVETA'
      if (countyName.includes('THARAKA') && countyName.includes('NITHI'))
        normalizedName = 'THARAKA NITHI'
      if (countyName.includes('ELGEYO') && countyName.includes('MARAKWET'))
        normalizedName = 'ELGEYO MARAKWET'
      if (countyName.includes('TRANS') && countyName.includes('NZOIA'))
        normalizedName = 'TRANS NZOIA'
      if (countyName.includes('UASIN') && countyName.includes('GISHU'))
        normalizedName = 'UASIN GISHU'
      if (countyName.includes('WEST') && countyName.includes('POKOT')) normalizedName = 'WEST POKOT'
      if (countyName.includes('HOMA') && countyName.includes('BAY')) normalizedName = 'HOMA BAY'
      if (countyName.includes('TANA') && countyName.includes('RIVER')) normalizedName = 'TANA RIVER'

      const countyData = countyInfo[normalizedName] || {
        name: normalizedName || countyName,
        code:
          geography.properties.COUNTY_COD?.toString() ||
          geography.properties.ID?.toString() ||
          'N/A',
      }

      onCountySelect(countyData)

      console.log('=== COUNTY CLICKED ===')
      console.log(`County Number: ${countyData.code}`)
      console.log(`County Name: ${countyData.name}`)
      if (countyData.population) console.log(`Population: ${countyData.population}`)
      if (countyData.area) console.log(`Area: ${countyData.area}`)
      console.log('=====================')
    },
    [onCountySelect],
  )

  const handleMouseEnter = useCallback(
    (event: React.MouseEvent, geography: { properties: Record<string, string | number> }) => {
      // Only show tooltip on desktop (screens larger than 768px)
      if (window.innerWidth < 768) return

      const rawCountyName =
        geography.properties.COUNTY ||
        geography.properties.NAME ||
        geography.properties.COUNTY_NAM ||
        geography.properties.name ||
        geography.properties.county ||
        'Unknown'

      let countyName = rawCountyName.toString().trim()

      // Handle special cases for display
      if (countyName.toUpperCase().includes('MURANG')) countyName = "Murang'a"
      if (countyName.toUpperCase().includes('TAITA') && countyName.toUpperCase().includes('TAVETA'))
        countyName = 'Taita Taveta'
      if (
        countyName.toUpperCase().includes('THARAKA') &&
        countyName.toUpperCase().includes('NITHI')
      )
        countyName = 'Tharaka Nithi'
      if (
        countyName.toUpperCase().includes('ELGEYO') &&
        countyName.toUpperCase().includes('MARAKWET')
      )
        countyName = 'Elgeyo Marakwet'
      if (countyName.toUpperCase().includes('TRANS') && countyName.toUpperCase().includes('NZOIA'))
        countyName = 'Trans Nzoia'
      if (countyName.toUpperCase().includes('UASIN') && countyName.toUpperCase().includes('GISHU'))
        countyName = 'Uasin Gishu'
      if (countyName.toUpperCase().includes('WEST') && countyName.toUpperCase().includes('POKOT'))
        countyName = 'West Pokot'
      if (countyName.toUpperCase().includes('HOMA') && countyName.toUpperCase().includes('BAY'))
        countyName = 'Homa Bay'
      if (countyName.toUpperCase().includes('TANA') && countyName.toUpperCase().includes('RIVER'))
        countyName = 'Tana River'

      setTooltip({
        content: countyName,
        x: event.clientX,
        y: event.clientY,
        visible: true,
      })
    },
    [],
  )

  const handleMouseLeave = useCallback(() => {
    setTooltip((prev) => ({ ...prev, visible: false }))
  }, [])

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (window.innerWidth < 768) return

    setTooltip((prev) => ({
      ...prev,
      x: event.clientX,
      y: event.clientY,
    }))
  }, [])

  return (
    <>
      {/* Map */}
      <div className="flex-1 flex items-center justify-center">
        <div
          style={{
            touchAction: 'none',
            userSelect: 'none',
            overflow: 'visible',
            width: '100%',
            height: '100%',
          }}
        >
          <ComposableMap
            projection="geoMercator"
            projectionConfig={{
              scale: 5800,
              center: [37.5, 0.2],
            }}
            width={1600}
            height={1200}
            className="w-full h-auto"
            style={{
              maxWidth: '100%',
              height: 'auto',
            }}
          >
            <Geographies geography={kenyaCountiesUrl}>
              {({ geographies }) =>
                geographies.map((geo) => {
                  const rawCountyName =
                    geo.properties.COUNTY ||
                    geo.properties.NAME ||
                    geo.properties.COUNTY_NAM ||
                    geo.properties.name ||
                    geo.properties.county ||
                    ''

                  let normalizedName = rawCountyName.toString().toUpperCase().trim()
                  if (normalizedName.includes('MURANG')) normalizedName = "MURANG'A"
                  if (normalizedName.includes('TAITA') && normalizedName.includes('TAVETA'))
                    normalizedName = 'TAITA TAVETA'
                  if (normalizedName.includes('THARAKA') && normalizedName.includes('NITHI'))
                    normalizedName = 'THARAKA NITHI'
                  if (normalizedName.includes('ELGEYO') && normalizedName.includes('MARAKWET'))
                    normalizedName = 'ELGEYO MARAKWET'
                  if (normalizedName.includes('TRANS') && normalizedName.includes('NZOIA'))
                    normalizedName = 'TRANS NZOIA'
                  if (normalizedName.includes('UASIN') && normalizedName.includes('GISHU'))
                    normalizedName = 'UASIN GISHU'
                  if (normalizedName.includes('WEST') && normalizedName.includes('POKOT'))
                    normalizedName = 'WEST POKOT'
                  if (normalizedName.includes('HOMA') && normalizedName.includes('BAY'))
                    normalizedName = 'HOMA BAY'
                  if (normalizedName.includes('TANA') && normalizedName.includes('RIVER'))
                    normalizedName = 'TANA RIVER'

                  const isSelected = selectedCounty?.name.toUpperCase() === normalizedName
                  const isRepresented = representedCounties.includes(normalizedName)

                  return (
                    <Geography
                      key={geo.rsmKey}
                      geography={geo}
                      onClick={() => handleCountyClick(geo)}
                      onMouseEnter={(event) => handleMouseEnter(event, geo)}
                      onMouseLeave={handleMouseLeave}
                      onMouseMove={handleMouseMove}
                      style={{
                        default: {
                          fill: isSelected ? '#7E2518' : isRepresented ? '#159147' : '#f3f4f6',
                          stroke: '#374151',
                          strokeWidth: 0.75,
                          outline: 'none',
                        },
                        hover: {
                          fill: isSelected ? '#C86E36' : isRepresented ? '#22c55e' : '#E8B32C',
                          stroke: '#1f2937',
                          strokeWidth: 1,
                          outline: 'none',
                          cursor: 'pointer',
                        },
                        pressed: {
                          fill: '#7E2518',
                          stroke: '#111827',
                          strokeWidth: 1,
                          outline: 'none',
                        },
                      }}
                    />
                  )
                })
              }
            </Geographies>
          </ComposableMap>
        </div>

        {/* Tooltip - Desktop only */}
        {tooltip.visible && (
          <div
            className="fixed pointer-events-none z-50 hidden md:block"
            style={{
              left: tooltip.x + 10,
              top: tooltip.y - 30,
            }}
          >
            <div className="bg-gray-900 text-white text-sm px-3 py-2 rounded-md shadow-lg border border-gray-700">
              {tooltip.content}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}
