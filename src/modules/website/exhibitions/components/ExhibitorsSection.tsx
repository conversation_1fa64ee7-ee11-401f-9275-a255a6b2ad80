'use client'

import { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ExternalLink } from 'lucide-react'
import FilterSection from './FilterSection'
import { thematicAreas } from '@/lib/registration-data'

const counties = ['MACHAKOS', 'MURANGA', 'TURKANA', 'KAKAMEGA', 'MOMBASA']

const exhibitors = {
  MACHAKOS: [
    {
      id: 1,
      name: 'Machakos Wild Herbal Tea Blend',
      description: 'Traditional tea blend used for digestive health',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Digestive Health',
      thematicArea: 'Traditional Medicine',
      county: 'MACHAKOS',
      image: '/api/placeholder/300/200',
    },
    {
      id: 2,
      name: 'Kamba Traditional Basketry',
      description: 'Handwoven baskets using indigenous techniques',
      tags: ['Traditional Crafts', 'Cultural Heritage'],
      category: 'Handicrafts',
      thematicArea: 'Cultural Heritage Preservation',
      county: 'MACHAKOS',
      image: '/api/placeholder/300/200',
    },
  ],
  MURANGA: [
    {
      id: 3,
      name: 'Kikuyu Traditional Honey',
      description: 'Pure honey from indigenous beekeeping practices',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Digestive Health',
      thematicArea: 'Traditional Medicine',
      county: 'MURANGA',
      image: '/api/placeholder/300/200',
    },
  ],
  TURKANA: [
    {
      id: 4,
      name: 'Turkana Livestock Medicine',
      description: 'Traditional veterinary practices for livestock',
      tags: ['Traditional Medicine', 'Livestock'],
      category: 'Animal Health',
      thematicArea: 'Traditional Medicine',
      county: 'TURKANA',
      image: '/api/placeholder/300/200',
    },
  ],
  KAKAMEGA: [
    {
      id: 5,
      name: 'Luhya Forest Conservation',
      description: 'Indigenous forest management techniques',
      tags: ['Environmental', 'Conservation'],
      category: 'Forest Management',
      thematicArea: 'Environmental Conservation',
      county: 'KAKAMEGA',
      image: '/api/placeholder/300/200',
    },
  ],
  MOMBASA: [
    {
      id: 6,
      name: 'Swahili Coastal Fishing',
      description: 'Traditional coastal fishing methods and tools',
      tags: ['Traditional Practices', 'Marine'],
      category: 'Fisheries',
      thematicArea: 'Traditional Knowledge Systems',
      county: 'MOMBASA',
      image: '/api/placeholder/300/200',
    },
  ],
}

export default function ExhibitorsSection() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCountyFilter, setSelectedCountyFilter] = useState('all')
  const [selectedThematicArea, setSelectedThematicArea] = useState('all')

  // Flatten all exhibitors for filtering
  const allExhibitors = useMemo(() => {
    return Object.values(exhibitors).flat()
  }, [])

  // Filter exhibitors based on search and filters
  const filteredExhibitors = useMemo(() => {
    return allExhibitors.filter((exhibitor) => {
      const matchesSearch =
        exhibitor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exhibitor.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exhibitor.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

      const matchesCounty = selectedCountyFilter === 'all' || exhibitor.county === selectedCountyFilter
      const matchesThematicArea = selectedThematicArea === 'all' || exhibitor.thematicArea === selectedThematicArea

      return matchesSearch && matchesCounty && matchesThematicArea
    })
  }, [allExhibitors, searchTerm, selectedCountyFilter, selectedThematicArea])

  // Get active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (searchTerm) count++
    if (selectedCountyFilter !== 'all') count++
    if (selectedThematicArea !== 'all') count++
    return count
  }, [searchTerm, selectedCountyFilter, selectedThematicArea])

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCountyFilter('all')
    setSelectedThematicArea('all')
  }

  // Determine which exhibitors to show
  const displayExhibitors = activeFiltersCount > 0
    ? filteredExhibitors
    : allExhibitors

  return (
    <section className="py-16 bg-gray-50" id="exhibitors-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4">
            EXPLORE ALL EXHIBITORS
            <br />
            BY COUNTIES
          </h2>
        </div>

        {/* Filter Section */}
        <FilterSection
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectedCounty={selectedCountyFilter}
          onCountyChange={setSelectedCountyFilter}
          selectedThematicArea={selectedThematicArea}
          onThematicAreaChange={setSelectedThematicArea}
          counties={counties}
          thematicAreas={thematicAreas}
          onClearFilters={clearFilters}
          activeFiltersCount={activeFiltersCount}
        />


        {/* Results Summary */}
        {activeFiltersCount > 0 && (
          <div className="text-center mb-6">
            <p className="text-gray-600">
              Showing {displayExhibitors.length} exhibitor{displayExhibitors.length !== 1 ? 's' : ''}
              {searchTerm && ` matching "${searchTerm}"`}
            </p>
          </div>
        )}

        {/* Exhibitor Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayExhibitors.length > 0 ? (
            displayExhibitors.map((exhibitor) => (
              <Card key={exhibitor.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="h-32 sm:h-36 bg-gray-200 flex items-center justify-center overflow-hidden">
                  <span className="text-gray-500">Exhibitor Image</span>
                </div>
                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-[#7E2518] mb-2">{exhibitor.name}</h3>
                    <p className="text-gray-600 mb-3">{exhibitor.description}</p>
                    {activeFiltersCount > 0 && (
                      <p className="text-sm text-gray-500 mb-2">
                        <span className="font-medium">County:</span> {exhibitor.county} |
                        <span className="font-medium"> Theme:</span> {exhibitor.thematicArea}
                      </p>
                    )}
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {exhibitor.tags.map((tag, tagIndex) => (
                      <Badge
                        key={tagIndex}
                        variant="secondary"
                        className="bg-[#E8B32C] text-[#7E2518]"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge className="bg-[#159147] text-white">{exhibitor.category}</Badge>
                    <Button size="sm" className="bg-[#7E2518] hover:bg-[#6B1F14]">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 text-lg">No exhibitors found matching your criteria.</p>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="mt-4 border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white"
              >
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
