'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ExternalLink, Building, Search, Star, Award, Trophy, Medal, Crown } from 'lucide-react'
import { sponsors } from '../../partners-sponsors/data/partners'
import type { FilterType, TierFilter } from '../../partners-sponsors/types'
import Image from 'next/image'

const filterOptions = [
  { value: 'all' as FilterType, label: 'All Categories' },
  { value: 'financial' as FilterType, label: 'Financial' },
  { value: 'service' as FilterType, label: 'Service' },
  { value: 'media' as FilterType, label: 'Media' },
  { value: 'special' as FilterType, label: 'Special Events' },
]

const tierOptions = [
  { value: 'all' as TierFilter, label: 'All Tiers', icon: Star },
  { value: 'title' as TierFilter, label: 'Title', icon: Crown },
  { value: 'platinum' as TierFilter, label: 'Platinum', icon: Award },
  { value: 'gold' as TierFilter, label: 'Gold', icon: Trophy },
  { value: 'silver' as TierFilter, label: 'Silver', icon: Medal },
  { value: 'bronze' as TierFilter, label: 'Bronze', icon: Building },
]

const tierColors = {
  title: 'from-[#7E2518] via-[#C86E36] to-[#E8B32C]',
  platinum: 'from-[#81B1DB] to-[#6a9bc7]',
  gold: 'from-[#E8B32C] to-[#d19d1f]',
  silver: 'from-gray-400 to-gray-500',
  bronze: 'from-[#C86E36] to-[#a85a2b]',
}

const categoryColors = {
  financial: 'from-[#159147] to-[#0f7a3a]',
  service: 'from-[#7E2518] to-[#5a1a10]',
  media: 'from-[#E8B32C] to-[#d19d1f]',
  special: 'from-[#81B1DB] to-[#6a9bc7]',
}

export default function SponsorsGridWithFilters() {
  const [categoryFilter, setCategoryFilter] = useState<FilterType>('all')
  const [tierFilter, setTierFilter] = useState<TierFilter>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [hoveredCard, setHoveredCard] = useState<string | null>(null)

  // Group sponsors by tier
  const sponsorsByTier = sponsors.reduce(
    (acc, sponsor) => {
      const tier = sponsor.tier || 'other'
      if (!acc[tier]) acc[tier] = []
      acc[tier].push(sponsor)
      return acc
    },
    {} as Record<string, typeof sponsors>,
  )

  const tierOrder = ['title', 'platinum', 'gold', 'silver', 'bronze', 'other']

  const filteredSponsors = sponsors.filter((sponsor) => {
    const matchesCategory = categoryFilter === 'all' || sponsor.category === categoryFilter
    const matchesTier = tierFilter === 'all' || sponsor.tier === tierFilter
    const matchesSearch =
      sponsor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sponsor.description.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesCategory && matchesTier && matchesSearch
  })

  return (
    <section className="py-16 lg:py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Filters Section */}
        <div className="mb-12">
          {/* Search Bar */}
          <div className="max-w-md mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Search sponsors..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-3 rounded-xl border-gray-200 focus:border-[#7E2518] focus:ring-[#7E2518]"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              />
            </div>
          </div>

          {/* Category Filters */}
          <div className="mb-6">
            <h3
              className="text-lg font-semibold text-[#7E2518] mb-4 text-center"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Filter by Category
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              {filterOptions.map((option) => {
                const isActive = categoryFilter === option.value
                return (
                  <Button
                    key={option.value}
                    variant={isActive ? 'default' : 'outline'}
                    onClick={() => setCategoryFilter(option.value)}
                    className={`rounded-full px-6 py-2 font-medium transition-all duration-300 ${
                      isActive
                        ? 'bg-gradient-to-r from-[#7E2518] to-[#159147] text-white shadow-lg hover:shadow-xl'
                        : 'border-[#7E2518]/30 text-[#7E2518] hover:bg-[#7E2518]/5'
                    }`}
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    {option.label}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Tier Filters */}
          <div className="mb-8">
            <h3
              className="text-lg font-semibold text-[#7E2518] mb-4 text-center"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Filter by Tier
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              {tierOptions.map((option) => {
                const IconComponent = option.icon
                const isActive = tierFilter === option.value
                return (
                  <Button
                    key={option.value}
                    variant={isActive ? 'default' : 'outline'}
                    onClick={() => setTierFilter(option.value)}
                    className={`flex items-center gap-2 rounded-full px-6 py-2 font-medium transition-all duration-300 ${
                      isActive
                        ? 'bg-gradient-to-r from-[#7E2518] to-[#159147] text-white shadow-lg hover:shadow-xl'
                        : 'border-[#7E2518]/30 text-[#7E2518] hover:bg-[#7E2518]/5'
                    }`}
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    <IconComponent className="w-4 h-4" />
                    {option.label}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Results Count */}
          <div className="text-center">
            <p className="text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              Showing {filteredSponsors.length} of {sponsors.length} sponsors
            </p>
          </div>
        </div>

        {/* Sponsors Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredSponsors.map((sponsor) => (
            <Card
              key={sponsor.id}
              className="group bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden"
              onMouseEnter={() => setHoveredCard(sponsor.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              {/* Background Gradient Overlay */}
              <div
                className={`absolute inset-0 bg-gradient-to-br ${sponsor.tier ? tierColors[sponsor.tier] : categoryColors[sponsor.category]} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
              ></div>

              {/* Animated Border Glow */}
              {hoveredCard === sponsor.id && (
                <div className="absolute -inset-1 bg-gradient-to-r from-[#159147]/20 via-[#E8B32C]/20 to-[#7E2518]/20 rounded-lg blur-sm animate-pulse"></div>
              )}

              <CardContent className="relative p-8">
                {/* Header with Logo and Badges */}
                <div className="flex items-start justify-between mb-6">
                  <div className="relative w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Image
                      src={sponsor.logo}
                      alt={sponsor.name}
                      width={48}
                      height={48}
                      className="object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        target.nextElementSibling?.classList.remove('hidden')
                      }}
                    />
                    <div className="hidden w-12 h-12 bg-gradient-to-br from-[#7E2518] to-[#159147] rounded-lg flex items-center justify-center">
                      <Building className="w-6 h-6 text-white" />
                    </div>
                  </div>

                  <div className="flex flex-col gap-2">
                    {sponsor.tier && (
                      <Badge
                        className={`bg-gradient-to-r ${tierColors[sponsor.tier]} text-white border-0 text-xs`}
                      >
                        {sponsor.tier}
                      </Badge>
                    )}
                    <Badge
                      variant="outline"
                      className={`bg-gradient-to-r ${categoryColors[sponsor.category]} text-white border-0 text-xs`}
                    >
                      {sponsor.category}
                    </Badge>
                  </div>
                </div>

                {/* Sponsor Name */}
                <h3
                  className="text-xl font-bold text-[#7E2518] mb-3 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-[#7E2518] group-hover:to-[#159147] transition-all duration-500"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  {sponsor.name}
                </h3>

                {/* Description */}
                <p
                  className="text-gray-600 leading-relaxed mb-6 text-sm"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  {sponsor.description}
                </p>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  {sponsor.website && (
                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className="flex-1 border-[#7E2518]/30 text-[#7E2518] hover:bg-[#7E2518] hover:text-white transition-all duration-300"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      <a
                        href={sponsor.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center gap-2"
                      >
                        <ExternalLink className="w-4 h-4" />
                        Visit
                      </a>
                    </Button>
                  )}

                  {sponsor.hasDetailsPage && (
                    <Button
                      asChild
                      size="sm"
                      className={`flex-1 bg-gradient-to-r ${sponsor.tier ? tierColors[sponsor.tier] : categoryColors[sponsor.category]} hover:shadow-lg text-white border-0 transition-all duration-300`}
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      <a
                        href={`/sponsors/${sponsor.id}`}
                        className="flex items-center justify-center gap-2"
                      >
                        Learn More
                      </a>
                    </Button>
                  )}
                </div>

                {/* Progress Bar Animation */}
                <div className="mt-6 h-1 bg-gray-100 rounded-full overflow-hidden">
                  <div
                    className={`h-full bg-gradient-to-r ${sponsor.tier ? tierColors[sponsor.tier] : categoryColors[sponsor.category]} rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out`}
                  ></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredSponsors.length === 0 && (
          <div className="text-center py-16">
            <Building className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3
              className="text-xl font-bold text-gray-500 mb-2"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              No sponsors found
            </h3>
            <p className="text-gray-400" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              Try adjusting your filters or search query
            </p>
          </div>
        )}
      </div>
    </section>
  )
}
