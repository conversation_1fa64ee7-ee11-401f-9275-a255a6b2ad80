'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Users, Building, Award, Search } from 'lucide-react'

export default function AllSponsorsHero() {
  return (
    <section className="relative py-16 lg:py-24 bg-gradient-to-br from-[#7E2518]/5 via-white to-[#159147]/5 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -left-40 w-60 h-60 bg-[#E8B32C]/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -right-40 w-72 h-72 bg-[#81B1DB]/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-[#159147]/3 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Conference Badge */}
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#7E2518]/8 to-[#159147]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
            <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
            <span
              className="text-sm font-medium text-[#7E2518]"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Our Valued Partners
            </span>
          </div>

          {/* Main Heading */}
          <h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#7E2518] mb-6 leading-tight"
            style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
          >
            Meet Our
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#7E2518] to-[#159147]">
              Sponsors
            </span>
          </h1>

          <div className="w-24 h-1 bg-gradient-to-r from-[#E8B32C] to-[#C86E36] rounded-full mx-auto mb-6"></div>

          <p
            className="text-lg md:text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto mb-12"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Discover the organizations and institutions supporting Kenya&apos;s first International
            Investment Conference on Indigenous Knowledge Intellectual Assets. Together, we&apos;re
            preserving heritage and driving innovation.
          </p>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="w-12 h-12 bg-gradient-to-br from-[#159147] to-[#0f7a3a] rounded-full flex items-center justify-center mx-auto mb-4">
                <Building className="w-6 h-6 text-white" />
              </div>
              <div
                className="text-3xl font-bold text-[#159147] mb-2"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                25+
              </div>
              <p
                className="text-gray-600 text-sm"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Partner Organizations
              </p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="w-12 h-12 bg-gradient-to-br from-[#E8B32C] to-[#d19d1f] rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-6 h-6 text-white" />
              </div>
              <div
                className="text-3xl font-bold text-[#E8B32C] mb-2"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                5
              </div>
              <p
                className="text-gray-600 text-sm"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Sponsorship Tiers
              </p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <div className="w-12 h-12 bg-gradient-to-br from-[#7E2518] to-[#5a1a10] rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div
                className="text-3xl font-bold text-[#7E2518] mb-2"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                500+
              </div>
              <p
                className="text-gray-600 text-sm"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Conference Delegates
              </p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              className="bg-gradient-to-r from-[#7E2518] to-[#159147] hover:shadow-lg text-white border-0 rounded-xl font-bold transition-all duration-500 transform hover:-translate-y-1 px-8 py-3"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              <Search className="w-5 h-5 mr-2" />
              Explore Sponsors
            </Button>
            <Button
              variant="outline"
              className="border-2 border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white rounded-xl font-bold transition-all duration-500 transform hover:-translate-y-1 px-8 py-3"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Become a Sponsor
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
