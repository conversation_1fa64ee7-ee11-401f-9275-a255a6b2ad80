'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ArrowRight, Download, Handshake, Users, Award, Target } from 'lucide-react'
import Link from 'next/link'

export default function SponsorshipCTA() {
  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-[#7E2518]/5 via-white to-[#159147]/5">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Main CTA Card */}
          <Card className="bg-gradient-to-br from-[#7E2518] to-[#159147] border-0 shadow-2xl overflow-hidden relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 right-0 w-64 h-64 bg-white rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white rounded-full translate-y-24 -translate-x-24"></div>
            </div>

            <CardContent className="relative p-12 lg:p-16 text-center text-white">
              {/* Icon */}
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-8">
                <Handshake className="w-10 h-10 text-white" />
              </div>

              {/* Heading */}
              <h2
                className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Ready to Make an
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#E8B32C] to-[#81B1DB]">
                  Impact?
                </span>
              </h2>

              <div className="w-24 h-1 bg-gradient-to-r from-[#E8B32C] to-[#81B1DB] rounded-full mx-auto mb-6"></div>

              <p
                className="text-xl text-white/90 leading-relaxed max-w-3xl mx-auto mb-12"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Join leading organizations in supporting Kenya&apos;s indigenous knowledge
                preservation and commercialization. Be part of a historic conference that bridges
                tradition with innovation.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
                <Button
                  asChild
                  size="lg"
                  className="bg-white text-[#7E2518] hover:bg-gray-100 rounded-xl font-bold transition-all duration-500 transform hover:-translate-y-1 px-8 py-4 shadow-lg hover:shadow-xl"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  <Link href="/partners-sponsors#sponsor" className="flex items-center gap-2">
                    <Handshake className="w-5 h-5" />
                    Become a Sponsor
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>

                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-2 border-white text-white hover:bg-white hover:text-[#7E2518] rounded-xl font-bold transition-all duration-500 transform hover:-translate-y-1 px-8 py-4 bg-transparent"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  <Link
                    href="/downloads/sponsorship-prospectus.pdf"
                    className="flex items-center gap-2"
                  >
                    <Download className="w-5 h-5" />
                    Download Prospectus
                  </Link>
                </Button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div
                    className="text-3xl font-bold mb-2"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    500+
                  </div>
                  <p
                    className="text-white/80 text-sm"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Expected Delegates
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="w-6 h-6 text-white" />
                  </div>
                  <div
                    className="text-3xl font-bold mb-2"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    25+
                  </div>
                  <p
                    className="text-white/80 text-sm"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Partner Organizations
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <div
                    className="text-3xl font-bold mb-2"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    40+
                  </div>
                  <p
                    className="text-white/80 text-sm"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Indigenous Knowledge Assets
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Benefits Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
            {[
              {
                icon: Users,
                title: 'Network Access',
                description: 'Connect with government officials, investors, and industry leaders',
                color: 'from-[#159147] to-[#0f7a3a]',
              },
              {
                icon: Award,
                title: 'Brand Visibility',
                description: 'Showcase your brand to a targeted, high-value audience',
                color: 'from-[#E8B32C] to-[#d19d1f]',
              },
              {
                icon: Target,
                title: 'Thought Leadership',
                description: 'Position your organization as a leader in cultural preservation',
                color: 'from-[#7E2518] to-[#5a1a10]',
              },
              {
                icon: Handshake,
                title: 'Partnership Opportunities',
                description: 'Discover new business opportunities and strategic partnerships',
                color: 'from-[#81B1DB] to-[#6a9bc7]',
              },
            ].map((benefit, index) => (
              <Card
                key={index}
                className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 group"
              >
                <CardContent className="p-6 text-center">
                  <div
                    className={`w-12 h-12 bg-gradient-to-br ${benefit.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <benefit.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3
                    className="font-bold text-[#7E2518] mb-2"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    {benefit.title}
                  </h3>
                  <p
                    className="text-gray-600 text-sm"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
