'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { ArrowRight, Download, Handshake } from 'lucide-react'

export default function HeroSection() {
  return (
    <section className="relative min-h-[80vh] flex items-center overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -left-40 w-60 h-60 bg-[#E8B32C]/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -right-40 w-72 h-72 bg-[#81B1DB]/5 rounded-full blur-3xl"></div>
      </div>

      {/* Background with clip-path */}
      <div
        className="absolute inset-0 bg-[#7E2518]"
        style={{ clipPath: 'polygon(0 0, 65% 0, 45% 100%, 0 100%)' }}
      />
      <div className="absolute inset-0 bg-white" />

      <div className="relative z-10 container mx-auto px-4 grid lg:grid-cols-2 gap-12 items-center">
        {/* Left Content */}
        <div className="lg:pr-8">
          {/* Conference Badge */}
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-white/20 to-white/10 px-4 py-2 rounded-full border border-white/30 mb-6">
            <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
            <span
              className="text-sm font-medium text-[#7E2518]"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Partnership Opportunities
            </span>
          </div>

          <h1
            className="text-4xl lg:text-6xl font-bold mb-6 leading-tight text-[#7E2518]"
            style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
          >
            Partners &
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#E8B32C] to-[#C86E36]">
              Sponsors
            </span>
          </h1>

          <div className="w-24 h-1 bg-[#E8B32C] rounded-full mb-6"></div>

          <p
            className="text-xl mb-8 text-gray-700 max-w-lg leading-relaxed"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Empowering collaboration through culture and heritage. Join us in showcasing
            Kenya&apos;s Indigenous Knowledge Intellectual Assets to the world.
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              asChild
              size="lg"
              className="bg-gradient-to-r from-[#159147] to-[#159147]/90 hover:from-[#159147]/90 hover:to-[#0f7a3a] text-white border-0 rounded-xl font-bold transition-all duration-500 shadow-lg hover:shadow-xl hover:shadow-[#159147]/25 transform hover:-translate-y-1 px-8"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              <Link href="#sponsor" className="flex items-center gap-2">
                <Handshake className="w-5 h-5" />
                Become a Sponsor
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-2 border-[#7E2518] text-[#7E2518] bg-white hover:bg-[#7E2518] hover:text-white rounded-xl font-bold transition-all duration-500 hover:shadow-lg transform hover:-translate-y-1 px-8"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              <Link
                href="/downloads/sponsorship-prospectus.pdf"
                className="flex items-center gap-2"
              >
                <Download className="w-5 h-5" />
                Download Prospectus
              </Link>
            </Button>
          </div>
        </div>

        {/* Right Content */}
        <div className="relative text-center lg:text-left">
          <div className="relative w-80 h-80 mx-auto lg:mx-0">
            {/* Main geometric container */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-transparent transform rotate-12 rounded-3xl shadow-2xl border border-white/20">
              {/* Inner pattern */}
              <div className="absolute inset-6 border border-[#E8B32C]/30 rounded-2xl"></div>
            </div>

            {/* Center icon */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-32 h-32 bg-gradient-to-br from-[#E8B32C] to-[#C86E36] rounded-full flex items-center justify-center shadow-2xl">
                <Handshake className="w-16 h-16 text-white" />
              </div>
            </div>

            {/* Floating elements */}
            <div className="absolute top-8 right-8 w-16 h-16 bg-gradient-to-br from-[#159147]/20 to-[#81B1DB]/20 transform rotate-12 rounded-xl border border-[#159147]/30 shadow-lg hover:scale-105 transition-transform duration-300"></div>
            <div className="absolute bottom-8 left-8 w-12 h-12 bg-gradient-to-br from-[#81B1DB]/20 to-[#159147]/20 transform -rotate-12 rounded-xl border border-[#81B1DB]/30 shadow-lg hover:scale-105 transition-transform duration-300"></div>

            {/* Small floating dots */}
            <div className="absolute top-1/4 right-16 w-3 h-3 bg-[#E8B32C]/60 rounded-full animate-pulse"></div>
            <div className="absolute bottom-1/4 left-16 w-3 h-3 bg-[#81B1DB]/60 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  )
}
