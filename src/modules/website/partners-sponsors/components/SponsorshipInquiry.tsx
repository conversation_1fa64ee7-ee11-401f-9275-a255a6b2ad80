'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Mail, Phone, MapPin, Send, CheckCircle } from 'lucide-react'

export default function SponsorshipInquiry() {
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    packageType: '',
    message: '',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    setIsSubmitted(true)
    setTimeout(() => setIsSubmitted(false), 3000)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-[#7E2518]/5 via-white to-[#159147]/5">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#7E2518]/8 to-[#159147]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
              <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
              <span
                className="text-sm font-medium text-[#7E2518]"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Get in Touch
              </span>
            </div>

            <h2
              className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Ready to
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#7E2518] to-[#159147]">
                Partner with Us?
              </span>
            </h2>

            <div className="w-24 h-1 bg-gradient-to-r from-[#E8B32C] to-[#C86E36] rounded-full mx-auto mb-6"></div>

            <p
              className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Contact our partnership team to discuss sponsorship opportunities and learn how you
              can be part of Kenya&apos;s first International Investment Conference on Indigenous
              Knowledge Intellectual Assets.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div className="space-y-8">
              <Card className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader>
                  <CardTitle
                    className="text-2xl font-bold text-[#7E2518] flex items-center gap-3"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    <Mail className="w-6 h-6" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-[#159147] rounded-full flex items-center justify-center">
                      <Phone className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p
                        className="font-semibold text-[#7E2518]"
                        style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                      >
                        Call or WhatsApp
                      </p>
                      <p
                        className="text-gray-600"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        +254 70 233 555
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-[#E8B32C] rounded-full flex items-center justify-center">
                      <Mail className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p
                        className="font-semibold text-[#7E2518]"
                        style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                      >
                        Email Us
                      </p>
                      <p
                        className="text-gray-600"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        <EMAIL>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-[#7E2518] rounded-full flex items-center justify-center">
                      <MapPin className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p
                        className="font-semibold text-[#7E2518]"
                        style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                      >
                        Visit Us
                      </p>
                      <p
                        className="text-gray-600"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        National Museums of Kenya
                        <br />
                        Museum Hill, Nairobi
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Benefits */}
              <Card className="bg-gradient-to-br from-[#159147]/5 to-[#E8B32C]/5 border border-[#159147]/20 shadow-lg">
                <CardContent className="p-8">
                  <h3
                    className="text-xl font-bold text-[#7E2518] mb-6"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    Why Partner with Us?
                  </h3>
                  <div className="space-y-4">
                    {[
                      'Access to 500+ high-profile delegates',
                      'Networking with government officials and investors',
                      'Brand visibility across multiple channels',
                      'Thought leadership opportunities',
                      'Cultural heritage preservation impact',
                    ].map((benefit, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-[#159147]" />
                        <span
                          className="text-gray-700"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {benefit}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Inquiry Form */}
            <Card className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <CardTitle
                  className="text-2xl font-bold text-[#7E2518]"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  Partnership Inquiry
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isSubmitted ? (
                  <div className="text-center py-8">
                    <CheckCircle className="w-16 h-16 text-[#159147] mx-auto mb-4" />
                    <h3
                      className="text-xl font-bold text-[#159147] mb-2"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      Thank You!
                    </h3>
                    <p
                      className="text-gray-600"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Your inquiry has been submitted. We&apos;ll get back to you within 24 hours.
                    </p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name" className="text-[#7E2518] font-medium">
                          Full Name *
                        </Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="email" className="text-[#7E2518] font-medium">
                          Email Address *
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="company" className="text-[#7E2518] font-medium">
                          Company/Organization *
                        </Label>
                        <Input
                          id="company"
                          value={formData.company}
                          onChange={(e) => handleInputChange('company', e.target.value)}
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone" className="text-[#7E2518] font-medium">
                          Phone Number
                        </Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="packageType" className="text-[#7E2518] font-medium">
                        Interested Package Type
                      </Label>
                      <Select onValueChange={(value) => handleInputChange('packageType', value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select a package type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="title">Title Sponsor</SelectItem>
                          <SelectItem value="platinum">Platinum Package</SelectItem>
                          <SelectItem value="gold">Gold Package</SelectItem>
                          <SelectItem value="silver">Silver Package</SelectItem>
                          <SelectItem value="bronze">Bronze Package</SelectItem>
                          <SelectItem value="service">Service Partnership</SelectItem>
                          <SelectItem value="media">Media Partnership</SelectItem>
                          <SelectItem value="special">Special Event Sponsorship</SelectItem>
                          <SelectItem value="custom">Custom Package</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="message" className="text-[#7E2518] font-medium">
                        Message
                      </Label>
                      <Textarea
                        id="message"
                        value={formData.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                        rows={4}
                        className="mt-1"
                        placeholder="Tell us about your organization and partnership interests..."
                      />
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-[#7E2518] to-[#159147] hover:shadow-lg text-white border-0 rounded-lg font-bold transition-all duration-500 transform hover:-translate-y-1 py-3"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      <Send className="w-5 h-5 mr-2" />
                      Send Inquiry
                    </Button>
                  </form>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
