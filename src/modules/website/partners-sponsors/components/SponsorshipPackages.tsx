'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Check, Star, Users, Camera, Gift, Crown, Award, Medal, Trophy, Zap } from 'lucide-react'
import { sponsorshipPackages } from '../data/sponsorship-packages'
import type { FilterType } from '../types'

const categoryIcons = {
  financial: Star,
  service: Gift,
  media: Camera,
  special: Zap,
}

const tierIcons = {
  title: Crown,
  platinum: Award,
  gold: Trophy,
  silver: Medal,
  bronze: Users,
}

const tierColors = {
  title: 'from-[#7E2518] via-[#C86E36] to-[#E8B32C]',
  platinum: 'from-[#81B1DB] to-[#6a9bc7]',
  gold: 'from-[#E8B32C] to-[#d19d1f]',
  silver: 'from-gray-400 to-gray-500',
  bronze: 'from-[#C86E36] to-[#a85a2b]',
}

export default function SponsorshipPackages() {
  const [activeFilter, setActiveFilter] = useState<FilterType>('financial')

  const filteredPackages = sponsorshipPackages.filter((pkg) => pkg.category === activeFilter)
  const financialPackages = sponsorshipPackages.filter((pkg) => pkg.category === 'financial')

  return (
    <section id="sponsor" className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#7E2518]/8 to-[#159147]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
            <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
            <span
              className="text-sm font-medium text-[#7E2518]"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Sponsorship Opportunities
            </span>
          </div>

          <h2
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6"
            style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
          >
            Sponsorship Tiers
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#7E2518] to-[#159147]">
              & Benefits
            </span>
          </h2>

          <div className="w-24 h-1 bg-gradient-to-r from-[#E8B32C] to-[#C86E36] rounded-full mx-auto mb-6"></div>

          <p
            className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Join us as a partner in Kenya&apos;s first International Investment Conference on
            Indigenous Knowledge Intellectual Assets. Choose from financial sponsorships, service
            partnerships, or media collaborations.
          </p>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {Object.entries(categoryIcons).map(([category, IconComponent]) => {
            const isActive = activeFilter === category
            return (
              <Button
                key={category}
                variant={isActive ? 'default' : 'outline'}
                onClick={() => setActiveFilter(category as FilterType)}
                className={`flex items-center gap-2 rounded-full px-6 py-3 font-medium transition-all duration-300 ${
                  isActive
                    ? 'bg-gradient-to-r from-[#7E2518] to-[#159147] text-white shadow-lg hover:shadow-xl'
                    : 'border-[#7E2518]/30 text-[#7E2518] hover:bg-[#7E2518]/5'
                }`}
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                <IconComponent className="w-4 h-4" />
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Button>
            )
          })}
        </div>

        {/* Financial Packages - Featured Grid */}
        {activeFilter === 'financial' && (
          <div className="grid lg:grid-cols-3 gap-8 mb-12">
            {financialPackages.slice(0, 3).map((pkg) => {
              const TierIcon = pkg.tier ? tierIcons[pkg.tier] : Star
              const isPopular = pkg.tier === 'platinum'

              return (
                <Card
                  key={pkg.id}
                  className={`group relative bg-white border-2 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-4 overflow-hidden ${
                    isPopular ? 'border-[#81B1DB] scale-105' : 'border-gray-200'
                  } ${pkg.featured ? 'ring-2 ring-[#E8B32C]/50' : ''}`}
                >
                  {/* Popular Badge */}
                  {isPopular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                      <Badge className="bg-gradient-to-r from-[#81B1DB] to-[#6a9bc7] text-white px-4 py-1 rounded-full font-bold">
                        Most Popular
                      </Badge>
                    </div>
                  )}

                  {/* Background Gradient */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-100 to-gray-200'} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                  ></div>

                  <CardHeader className="relative text-center pb-4">
                    <div
                      className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-400 to-gray-500'} rounded-full flex items-center justify-center shadow-lg`}
                    >
                      <TierIcon className="w-8 h-8 text-white" />
                    </div>

                    <CardTitle
                      className="text-2xl font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      {pkg.name}
                    </CardTitle>

                    <div className="text-3xl font-bold text-[#159147] mb-2">{pkg.price}</div>

                    <p
                      className="text-gray-600 text-sm"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {pkg.description}
                    </p>
                  </CardHeader>

                  <CardContent className="relative">
                    <div className="space-y-3 mb-8">
                      {pkg.benefits.slice(0, 6).map((benefit, idx) => (
                        <div key={idx} className="flex items-start gap-3">
                          <div
                            className={`w-5 h-5 rounded-full bg-gradient-to-r ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-400 to-gray-500'} flex items-center justify-center flex-shrink-0 mt-0.5`}
                          >
                            <Check className="w-3 h-3 text-white" />
                          </div>
                          <span
                            className="text-gray-700 text-sm leading-relaxed"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {benefit}
                          </span>
                        </div>
                      ))}
                      {pkg.benefits.length > 6 && (
                        <div className="text-center">
                          <span className="text-[#7E2518] font-medium text-sm">
                            +{pkg.benefits.length - 6} more benefits
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="text-center mb-6">
                      <div className="text-sm text-gray-600 mb-2">
                        {pkg.currentPartners} of {pkg.maxPartners} spots taken
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full bg-gradient-to-r ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-400 to-gray-500'}`}
                          style={{ width: `${(pkg.currentPartners / pkg.maxPartners) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <Button
                      className={`w-full bg-gradient-to-r ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-400 to-gray-500'} hover:shadow-lg text-white border-0 rounded-lg font-bold transition-all duration-500 transform group-hover:scale-105`}
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      Choose {pkg.name}
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}

        {/* Other Categories - List View */}
        {activeFilter !== 'financial' && (
          <div className="grid md:grid-cols-2 gap-8">
            {filteredPackages.map((pkg) => (
              <Card
                key={pkg.id}
                className="group bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 overflow-hidden"
              >
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-[#7E2518] to-[#159147] rounded-lg flex items-center justify-center">
                      {React.createElement(categoryIcons[pkg.category], {
                        className: 'w-6 h-6 text-white',
                      })}
                    </div>
                    <div>
                      <CardTitle
                        className="text-xl font-bold text-[#7E2518]"
                        style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                      >
                        {pkg.name}
                      </CardTitle>
                      <div className="text-lg font-semibold text-[#159147]">{pkg.price}</div>
                    </div>
                  </div>
                  <p
                    className="text-gray-600 mt-2"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {pkg.description}
                  </p>
                </CardHeader>

                <CardContent>
                  <div className="space-y-2 mb-6">
                    {pkg.benefits.map((benefit, idx) => (
                      <div key={idx} className="flex items-start gap-3">
                        <Check className="w-4 h-4 text-[#159147] flex-shrink-0 mt-0.5" />
                        <span
                          className="text-gray-700 text-sm"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {benefit}
                        </span>
                      </div>
                    ))}
                  </div>

                  <Button
                    className="w-full bg-gradient-to-r from-[#7E2518] to-[#159147] hover:shadow-lg text-white border-0 rounded-lg font-bold transition-all duration-500"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    Apply for {pkg.name}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </section>
  )
}
