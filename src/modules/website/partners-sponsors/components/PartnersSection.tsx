'use client'

import { Building, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { partners } from '../data/partners'
import Image from 'next/image'
import Link from 'next/link'

export default function PartnersSection() {
  // Create placeholder logos for demonstration (3 rows)
  const allPartners = [...partners, ...partners, ...partners] // Triple for more logos

  // Split into 3 rows
  const partnersPerRow = Math.ceil(allPartners.length / 3)
  const row1 = allPartners.slice(0, partnersPerRow)
  const row2 = allPartners.slice(partnersPerRow, partnersPerRow * 2)
  const row3 = allPartners.slice(partnersPerRow * 2)

  const renderPartnerRow = (rowPartners: typeof partners, rowIndex: number) => (
    <div key={rowIndex} className="flex flex-wrap justify-center items-center gap-8 mb-8">
      {rowPartners.map((partner, index) => (
        <div
          key={`${partner.id}-${rowIndex}-${index}`}
          className="flex-shrink-0 group cursor-pointer"
        >
          <div className="w-32 h-20 bg-gray-50 rounded-lg flex items-center justify-center p-4 group-hover:bg-gray-100 transition-colors duration-300 group-hover:shadow-lg border border-gray-100">
            <Image
              src={partner.logo}
              alt={partner.name}
              width={96}
              height={64}
              className="object-contain max-w-full max-h-full filter grayscale group-hover:grayscale-0 transition-all duration-300"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.style.display = 'none'
                target.nextElementSibling?.classList.remove('hidden')
              }}
            />
            <div className="hidden w-16 h-12 bg-[#7E2518] rounded flex items-center justify-center">
              <Building className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <section className="py-16 lg:py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-[#7E2518]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
            <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
            <span
              className="text-sm font-medium text-[#7E2518]"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Our Partners
            </span>
          </div>

          <h2
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6"
            style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
          >
            Strategic
            <span className="block text-[#159147]">Partnerships</span>
          </h2>

          <div className="w-24 h-1 bg-[#E8B32C] rounded-full mx-auto mb-6"></div>

          <p
            className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            We collaborate with leading organizations, institutions, and communities to advance
            indigenous knowledge preservation and commercialization across Kenya and beyond.
          </p>
        </div>

        {/* Partners Logo Rows */}
        <div className="max-w-6xl mx-auto">
          {renderPartnerRow(row1, 1)}
          {renderPartnerRow(row2, 2)}
          {renderPartnerRow(row3, 3)}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 max-w-2xl mx-auto">
            <h3
              className="text-2xl font-bold text-[#7E2518] mb-4"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Want to See All Our Partners?
            </h3>
            <p
              className="text-gray-600 mb-6"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Explore our complete partner directory with detailed information about each
              organization.
            </p>
            <Button
              asChild
              className="bg-[#7E2518] hover:bg-[#159147] text-white border-0 rounded-xl font-bold transition-all duration-500 transform hover:-translate-y-1 px-8 py-3"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              <Link href="/partners" className="flex items-center gap-2">
                View All Partners
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
