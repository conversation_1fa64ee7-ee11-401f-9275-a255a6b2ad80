'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowRight, Crown, Award, Trophy, Medal, Building } from 'lucide-react'
import { sponsors } from '../data/partners'
import Image from 'next/image'
import Link from 'next/link'

const tierIcons = {
  title: Crown,
  platinum: Award,
  gold: Trophy,
  silver: Medal,
  bronze: Building,
}

const tierColors = {
  title: 'bg-[#7E2518]',
  platinum: 'bg-[#81B1DB]',
  gold: 'bg-[#E8B32C]',
  silver: 'bg-gray-400',
  bronze: 'bg-[#C86E36]',
}

export default function SponsorsShowcase() {
  // Group sponsors by tier
  const sponsorsByTier = sponsors.reduce(
    (acc, sponsor) => {
      const tier = sponsor.tier || 'other'
      if (!acc[tier]) acc[tier] = []
      acc[tier].push(sponsor)
      return acc
    },
    {} as Record<string, typeof sponsors>,
  )

  const tierOrder = ['title', 'platinum', 'gold', 'silver', 'bronze', 'other']

  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#7E2518]/8 to-[#159147]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
            <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
            <span
              className="text-sm font-medium text-[#7E2518]"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Our Sponsors
            </span>
          </div>

          <h2
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6"
            style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
          >
            Conference
            <span className="block text-[#159147]">Sponsors</span>
          </h2>

          <div className="w-24 h-1 bg-[#E8B32C] rounded-full mx-auto mb-6"></div>

          <p
            className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Meet the organizations supporting Kenya&apos;s first International Investment Conference
            on Indigenous Knowledge Intellectual Assets.
          </p>
        </div>

        {/* Sponsors by Tier */}
        <div className="space-y-12">
          {tierOrder.map((tier) => {
            const tierSponsors = sponsorsByTier[tier]
            if (!tierSponsors || tierSponsors.length === 0) return null

            const TierIcon = tierIcons[tier as keyof typeof tierIcons] || Building
            const tierColor = tierColors[tier as keyof typeof tierColors] || 'bg-gray-400'

            return (
              <div key={tier} className="relative">
                {/* Tier Header */}
                <div className="flex items-center justify-center mb-8">
                  <div className="flex items-center gap-4">
                    <div
                      className={`w-12 h-12 ${tierColor} rounded-full flex items-center justify-center shadow-lg`}
                    >
                      <TierIcon className="w-6 h-6 text-white" />
                    </div>
                    <h3
                      className="text-2xl font-bold text-[#7E2518] capitalize"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      {tier === 'other' ? 'Service & Media Partners' : `${tier} Sponsors`}
                    </h3>
                  </div>
                </div>

                {/* Static Logo Grid */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 py-8">
                  {/* Special handling for title sponsors with multiple logos */}
                  {tier === 'title' ? (
                    <div className="flex justify-center">
                      {tierSponsors.map((sponsor, index) => {
                        // Check if sponsor has multiple logos (like Safaricom + M-Pesa)
                        const hasMultipleLogos =
                          sponsor.additionalLogos && sponsor.additionalLogos.length > 0

                        return (
                          <div
                            key={`${sponsor.id}-${index}`}
                            className={`group cursor-pointer ${hasMultipleLogos ? 'bg-white rounded-xl shadow-lg border border-gray-100 p-6' : ''}`}
                          >
                            {hasMultipleLogos ? (
                              // Multiple logos in one card with vertical separator
                              <div className="flex items-center gap-6">
                                <div className="flex items-center justify-center">
                                  <Image
                                    src={sponsor.logo}
                                    alt={sponsor.name}
                                    width={120}
                                    height={80}
                                    className="object-contain max-w-full max-h-full"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement
                                      target.style.display = 'none'
                                      target.nextElementSibling?.classList.remove('hidden')
                                    }}
                                  />
                                  <div className="hidden w-20 h-16 bg-[#7E2518] rounded flex items-center justify-center">
                                    <Building className="w-10 h-10 text-white" />
                                  </div>
                                </div>

                                {/* Vertical separator */}
                                <div className="w-px h-16 bg-gray-300"></div>

                                {sponsor.additionalLogos?.map((additionalLogo, logoIndex) => (
                                  <div key={logoIndex} className="flex items-center justify-center">
                                    <Image
                                      src={additionalLogo.logo}
                                      alt={additionalLogo.name}
                                      width={120}
                                      height={80}
                                      className="object-contain max-w-full max-h-full"
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement
                                        target.style.display = 'none'
                                        target.nextElementSibling?.classList.remove('hidden')
                                      }}
                                    />
                                    <div className="hidden w-20 h-16 bg-[#7E2518] rounded flex items-center justify-center">
                                      <Building className="w-10 h-10 text-white" />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              // Single logo
                              <div className="w-40 h-24 bg-gray-50 rounded-lg flex items-center justify-center p-4 group-hover:bg-gray-100 transition-colors duration-300 group-hover:shadow-lg">
                                <Image
                                  src={sponsor.logo}
                                  alt={sponsor.name}
                                  width={128}
                                  height={80}
                                  className="object-contain max-w-full max-h-full"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement
                                    target.style.display = 'none'
                                    target.nextElementSibling?.classList.remove('hidden')
                                  }}
                                />
                                <div className="hidden w-20 h-16 bg-[#7E2518] rounded flex items-center justify-center">
                                  <Building className="w-10 h-10 text-white" />
                                </div>
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    // Regular grid for other tiers
                    <div className="flex flex-wrap justify-center items-center gap-8">
                      {tierSponsors.map((sponsor, index) => (
                        <div
                          key={`${sponsor.id}-${index}`}
                          className="flex-shrink-0 group cursor-pointer"
                        >
                          <div className="w-32 h-20 bg-gray-50 rounded-lg flex items-center justify-center p-4 group-hover:bg-gray-100 transition-colors duration-300 group-hover:shadow-lg">
                            <Image
                              src={sponsor.logo}
                              alt={sponsor.name}
                              width={96}
                              height={64}
                              className="object-contain max-w-full max-h-full filter grayscale group-hover:grayscale-0 transition-all duration-300"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement
                                target.style.display = 'none'
                                target.nextElementSibling?.classList.remove('hidden')
                              }}
                            />
                            <div className="hidden w-16 h-12 bg-[#7E2518] rounded flex items-center justify-center">
                              <Building className="w-8 h-8 text-white" />
                            </div>
                          </div>
                          <p
                            className="text-center text-sm text-gray-600 mt-2 group-hover:text-[#7E2518] transition-colors duration-300"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {sponsor.name}
                          </p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 max-w-2xl mx-auto">
            <h3
              className="text-2xl font-bold text-[#7E2518] mb-4"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Want to See All Our Sponsors?
            </h3>
            <p
              className="text-gray-600 mb-6"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Explore our complete sponsor directory with detailed information about each partner.
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-[#7E2518] to-[#159147] hover:shadow-lg text-white border-0 rounded-xl font-bold transition-all duration-500 transform hover:-translate-y-1 px-8 py-3"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              <Link href="/sponsors" className="flex items-center gap-2">
                View All Sponsors
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
