'use client'

import React from 'react'
import Image from 'next/image'
import { 
  <PERSON><PERSON>, 
  Dialog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle,
  DialogClose 
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { 
  MapPin, 
  Star, 
  Award, 
  Building, 
  TrendingUp, 
  Users,
  X,
  ExternalLink,
  Lightbulb
} from 'lucide-react'
import { ProfileModalProps, isInvestorProfile, isExhibitorProfile } from '../types'

export default function ProfileModal({ profile, isOpen, onClose, type }: ProfileModalProps) {
  if (!profile) return null

  const isInvestor = isInvestorProfile(profile)
  const isExhibitor = isExhibitorProfile(profile)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="sr-only">
            {profile.name} - {type === 'investor' ? 'Investor' : 'Exhibitor'} Profile
          </DialogTitle>
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogHeader>

        <div className="grid md:grid-cols-2 gap-8 p-6">
          {/* Profile Image and Basic Info */}
          <div className="space-y-6">
            <div className="relative">
              <div className="w-full h-80 rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200">
                <Image
                  src={profile.image}
                  alt={profile.name}
                  width={400}
                  height={400}
                  className="w-full h-full object-cover"
                />
              </div>
              {profile.verified && (
                <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                  <Award className="w-4 h-4" />
                  Verified
                </div>
              )}
              <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                {profile.badge}
              </div>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(profile.rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-lg font-semibold">{profile.rating}</span>
              <span className="text-gray-600">rating</span>
            </div>

            {/* Additional Profile Stats */}
            <div className="bg-gradient-to-r from-[#7E2518]/5 to-[#159147]/5 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-3">Profile Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className="font-medium text-green-600">
                    {profile.verified ? 'Verified' : 'Pending Verification'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Badge Level:</span>
                  <span className="font-medium text-[#7E2518]">{profile.badge}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rating:</span>
                  <span className="font-medium text-yellow-600">{profile.rating}/5.0</span>
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Information */}
          <div className="space-y-6">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">{profile.name}</h2>
              <p className="text-xl text-gray-600 mb-1">{profile.title}</p>
              <div className="flex items-center gap-2 text-gray-600 mb-4">
                <Building className="w-4 h-4" />
                <span>{profile.organization}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <MapPin className="w-4 h-4" />
                <span>{profile.location}</span>
              </div>
            </div>

            {/* Focus Area */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Lightbulb className="w-5 h-5 text-yellow-500" />
                <h3 className="font-semibold text-gray-900">Focus Area</h3>
              </div>
              <p className="text-gray-700">{profile.focus}</p>
            </div>

            {/* Type-specific Information */}
            <div className="grid grid-cols-2 gap-4">
              {isInvestor && (
                <>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="w-5 h-5 text-blue-500" />
                      <h4 className="font-semibold text-gray-900">Investment</h4>
                    </div>
                    <p className="text-gray-700">{profile.investment}</p>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="w-5 h-5 text-green-500" />
                      <h4 className="font-semibold text-gray-900">Projects</h4>
                    </div>
                    <p className="text-gray-700">{profile.projects}</p>
                  </div>
                </>
              )}

              {isExhibitor && (
                <>
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Award className="w-5 h-5 text-purple-500" />
                      <h4 className="font-semibold text-gray-900">Achievement</h4>
                    </div>
                    <p className="text-gray-700">{profile.achievement}</p>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="w-5 h-5 text-orange-500" />
                      <h4 className="font-semibold text-gray-900">Projects</h4>
                    </div>
                    <p className="text-gray-700">{profile.projects}</p>
                  </div>
                </>
              )}
            </div>

            {/* Detailed Information Section */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-3">Additional Details</h4>
              <div className="space-y-3 text-sm">
                {isInvestor && (
                  <>
                    <div>
                      <span className="font-medium text-gray-700">Investment Range:</span>
                      <p className="text-gray-600 mt-1">{profile.investment}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Active Projects:</span>
                      <p className="text-gray-600 mt-1">{profile.projects}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Investment Focus:</span>
                      <p className="text-gray-600 mt-1">Sustainable development, Indigenous Knowledge preservation, and community empowerment initiatives.</p>
                    </div>
                  </>
                )}
                {isExhibitor && (
                  <>
                    <div>
                      <span className="font-medium text-gray-700">Key Achievement:</span>
                      <p className="text-gray-600 mt-1">{profile.achievement}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Projects Completed:</span>
                      <p className="text-gray-600 mt-1">{profile.projects}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Expertise:</span>
                      <p className="text-gray-600 mt-1">Traditional knowledge preservation, cultural heritage documentation, and community education programs.</p>
                    </div>
                  </>
                )}
                <div>
                  <span className="font-medium text-gray-700">Contact Information:</span>
                  <p className="text-gray-600 mt-1">Available through the IKIA platform for verified partnerships and collaborations.</p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button className="flex-1 bg-[#7E2518] hover:bg-[#7E2518]/90">
                <ExternalLink className="w-4 h-4 mr-2" />
                Connect & Partner
              </Button>
              <Button variant="outline" className="flex-1">
                Send Message
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
