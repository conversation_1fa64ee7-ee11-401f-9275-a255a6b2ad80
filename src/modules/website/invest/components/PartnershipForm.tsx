"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Send, User, Building, Lightbulb, Heart, CheckCircle } from "lucide-react"

const formSteps = [
  { id: 1, title: "Partnership Type", icon: User },
  { id: 2, title: "Contact Details", icon: Building },
  { id: 3, title: "Project Information", icon: Lightbulb },
  { id: 4, title: "Review & Submit", icon: CheckCircle },
]

export default function PartnershipForm() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    userType: "ik-holder",
    lookingFor: "investor",
    fullName: "",
    email: "",
    phone: "",
    organization: "",
    areaOfInterest: "",
    investmentRange: "",
    timeline: "",
    experience: "",
    message: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (currentStep < formSteps.length) {
      setCurrentStep(currentStep + 1)
    } else {
      console.log("Partnership form submitted:", formData)
      // Handle form submission
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const goToStep = (step: number) => {
    setCurrentStep(step)
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-[#E8B32C]/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#159147]/10 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-5xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-[#7E2518] mb-6">
              Partnership <span className="text-[#159147]">Matching</span>
            </h2>
            <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Connect with the right partners through our intelligent matching system. Tell us about your goals and
              we'll help you find the perfect collaboration opportunities.
            </p>
          </div>

          {/* Enhanced Form Header */}
          <div className="bg-gradient-to-r from-[#7E2518] to-[#C86E36] text-white rounded-t-2xl p-8">
            <h3 className="text-2xl md:text-3xl font-bold text-center mb-8">PARTNERSHIP MATCH MAKING FORM</h3>

            {/* Progress Steps */}
            <div className="flex items-center justify-between max-w-2xl mx-auto">
              {formSteps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <button
                    onClick={() => goToStep(step.id)}
                    className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                      currentStep >= step.id
                        ? "bg-white text-[#7E2518] shadow-lg"
                        : "bg-white/20 text-white/70 hover:bg-white/30"
                    }`}
                  >
                    <step.icon className="w-6 h-6" />
                  </button>
                  {index < formSteps.length - 1 && (
                    <div
                      className={`w-8 md:w-16 h-1 mx-2 transition-all duration-300 ${
                        currentStep > step.id ? "bg-white" : "bg-white/30"
                      }`}
                    ></div>
                  )}
                </div>
              ))}
            </div>

            <div className="text-center mt-4">
              <p className="text-white/90">
                Step {currentStep} of {formSteps.length}: {formSteps[currentStep - 1]?.title}
              </p>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white border border-gray-200 rounded-b-2xl shadow-2xl overflow-hidden">
            <form onSubmit={handleSubmit} className="p-8 lg:p-12">
              {/* Step 1: Partnership Type */}
              {currentStep === 1 && (
                <div className="space-y-8">
                  <div className="text-center mb-8">
                    <User className="w-16 h-16 text-[#7E2518] mx-auto mb-4" />
                    <h4 className="text-2xl font-bold text-[#7E2518] mb-2">Tell us about yourself</h4>
                    <p className="text-gray-600">Help us understand your role and what you're looking for</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8">
                    {/* I am a */}
                    <div className="bg-gray-50 rounded-xl p-6">
                      <label className="block text-lg font-bold text-[#7E2518] mb-4">I am a:</label>
                      <div className="space-y-3">
                        {[
                          { value: "ik-holder", label: "Indigenous Knowledge Holder", icon: "🌿" },
                          { value: "investor", label: "Investor", icon: "💰" },
                          { value: "entrepreneur", label: "Entrepreneur", icon: "🚀" },
                          { value: "researcher", label: "Researcher", icon: "🔬" },
                        ].map((option) => (
                          <label
                            key={option.value}
                            className="flex items-center p-3 bg-white rounded-lg border-2 border-transparent hover:border-[#7E2518]/20 cursor-pointer transition-all"
                          >
                            <input
                              type="radio"
                              name="userType"
                              value={option.value}
                              checked={formData.userType === option.value}
                              onChange={handleChange}
                              className="sr-only"
                            />
                            <div
                              className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
                                formData.userType === option.value ? "border-[#7E2518] bg-[#7E2518]" : "border-gray-300"
                              }`}
                            >
                              {formData.userType === option.value && (
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                              )}
                            </div>
                            <span className="text-2xl mr-3">{option.icon}</span>
                            <span className="text-gray-700 font-medium">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* I am looking for */}
                    <div className="bg-gray-50 rounded-xl p-6">
                      <label className="block text-lg font-bold text-[#7E2518] mb-4">I am looking for:</label>
                      <div className="space-y-3">
                        {[
                          { value: "investor", label: "Investment Partner", icon: "💼" },
                          { value: "knowledge-holder", label: "Knowledge Holder", icon: "🧠" },
                          { value: "business-partner", label: "Business Partner", icon: "🤝" },
                          { value: "mentor", label: "Mentor/Advisor", icon: "👨‍🏫" },
                        ].map((option) => (
                          <label
                            key={option.value}
                            className="flex items-center p-3 bg-white rounded-lg border-2 border-transparent hover:border-[#7E2518]/20 cursor-pointer transition-all"
                          >
                            <input
                              type="radio"
                              name="lookingFor"
                              value={option.value}
                              checked={formData.lookingFor === option.value}
                              onChange={handleChange}
                              className="sr-only"
                            />
                            <div
                              className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
                                formData.lookingFor === option.value
                                  ? "border-[#159147] bg-[#159147]"
                                  : "border-gray-300"
                              }`}
                            >
                              {formData.lookingFor === option.value && (
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                              )}
                            </div>
                            <span className="text-2xl mr-3">{option.icon}</span>
                            <span className="text-gray-700 font-medium">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Contact Details */}
              {currentStep === 2 && (
                <div className="space-y-8">
                  <div className="text-center mb-8">
                    <Building className="w-16 h-16 text-[#7E2518] mx-auto mb-4" />
                    <h4 className="text-2xl font-bold text-[#7E2518] mb-2">Contact Information</h4>
                    <p className="text-gray-600">
                      Provide your contact details so we can connect you with the right partners
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="fullName" className="block text-sm font-bold text-[#7E2518] mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="fullName"
                        name="fullName"
                        required
                        value={formData.fullName}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-bold text-[#7E2518] mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                        placeholder="Enter your email address"
                      />
                    </div>

                    <div>
                      <label htmlFor="phone" className="block text-sm font-bold text-[#7E2518] mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                        placeholder="Enter your phone number"
                      />
                    </div>

                    <div>
                      <label htmlFor="organization" className="block text-sm font-bold text-[#7E2518] mb-2">
                        Organization
                      </label>
                      <input
                        type="text"
                        id="organization"
                        name="organization"
                        value={formData.organization}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                        placeholder="Enter your organization name"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Project Information */}
              {currentStep === 3 && (
                <div className="space-y-8">
                  <div className="text-center mb-8">
                    <Lightbulb className="w-16 h-16 text-[#7E2518] mx-auto mb-4" />
                    <h4 className="text-2xl font-bold text-[#7E2518] mb-2">Project Details</h4>
                    <p className="text-gray-600">Tell us about your interests and project requirements</p>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <label htmlFor="areaOfInterest" className="block text-sm font-bold text-[#7E2518] mb-2">
                        Area of Interest *
                      </label>
                      <select
                        id="areaOfInterest"
                        name="areaOfInterest"
                        required
                        value={formData.areaOfInterest}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                      >
                        <option value="">Select your area of interest</option>
                        <option value="agriculture">Traditional Agriculture</option>
                        <option value="medicine">Traditional Medicine</option>
                        <option value="crafts">Traditional Crafts</option>
                        <option value="tourism">Cultural Tourism</option>
                        <option value="technology">Technology Integration</option>
                        <option value="education">Education & Research</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="investmentRange" className="block text-sm font-bold text-[#7E2518] mb-2">
                          Investment Range (USD)
                        </label>
                        <select
                          id="investmentRange"
                          name="investmentRange"
                          value={formData.investmentRange}
                          onChange={handleChange}
                          className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                        >
                          <option value="">Select investment range</option>
                          <option value="under-50k">Under $50,000</option>
                          <option value="50k-100k">$50,000 - $100,000</option>
                          <option value="100k-500k">$100,000 - $500,000</option>
                          <option value="500k-1m">$500,000 - $1,000,000</option>
                          <option value="over-1m">Over $1,000,000</option>
                        </select>
                      </div>

                      <div>
                        <label htmlFor="timeline" className="block text-sm font-bold text-[#7E2518] mb-2">
                          Project Timeline
                        </label>
                        <select
                          id="timeline"
                          name="timeline"
                          value={formData.timeline}
                          onChange={handleChange}
                          className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                        >
                          <option value="">Select timeline</option>
                          <option value="immediate">Immediate (0-3 months)</option>
                          <option value="short-term">Short-term (3-12 months)</option>
                          <option value="medium-term">Medium-term (1-3 years)</option>
                          <option value="long-term">Long-term (3+ years)</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="experience" className="block text-sm font-bold text-[#7E2518] mb-2">
                        Relevant Experience
                      </label>
                      <textarea
                        id="experience"
                        name="experience"
                        rows={4}
                        value={formData.experience}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors resize-none"
                        placeholder="Describe your relevant experience, previous projects, or expertise..."
                      />
                    </div>

                    <div>
                      <label htmlFor="message" className="block text-sm font-bold text-[#7E2518] mb-2">
                        Project Description & Goals *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        required
                        rows={6}
                        value={formData.message}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors resize-none"
                        placeholder="Tell us about your partnership interests, investment goals, project vision, or how you'd like to collaborate..."
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 4: Review & Submit */}
              {currentStep === 4 && (
                <div className="space-y-8">
                  <div className="text-center mb-8">
                    <CheckCircle className="w-16 h-16 text-[#159147] mx-auto mb-4" />
                    <h4 className="text-2xl font-bold text-[#7E2518] mb-2">Review Your Information</h4>
                    <p className="text-gray-600">
                      Please review your details before submitting your partnership request
                    </p>
                  </div>

                  <div className="bg-gray-50 rounded-xl p-6 space-y-4">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h5 className="font-bold text-[#7E2518] mb-2">Partnership Type</h5>
                        <p className="text-gray-700">
                          I am a: <span className="font-semibold">{formData.userType}</span>
                        </p>
                        <p className="text-gray-700">
                          Looking for: <span className="font-semibold">{formData.lookingFor}</span>
                        </p>
                      </div>
                      <div>
                        <h5 className="font-bold text-[#7E2518] mb-2">Contact Information</h5>
                        <p className="text-gray-700">{formData.fullName}</p>
                        <p className="text-gray-700">{formData.email}</p>
                        {formData.organization && <p className="text-gray-700">{formData.organization}</p>}
                      </div>
                    </div>
                    <div>
                      <h5 className="font-bold text-[#7E2518] mb-2">Project Details</h5>
                      <p className="text-gray-700">
                        Area: <span className="font-semibold">{formData.areaOfInterest}</span>
                      </p>
                      {formData.investmentRange && (
                        <p className="text-gray-700">
                          Investment Range: <span className="font-semibold">{formData.investmentRange}</span>
                        </p>
                      )}
                      {formData.timeline && (
                        <p className="text-gray-700">
                          Timeline: <span className="font-semibold">{formData.timeline}</span>
                        </p>
                      )}
                    </div>
                    {formData.message && (
                      <div>
                        <h5 className="font-bold text-[#7E2518] mb-2">Message</h5>
                        <p className="text-gray-700 text-sm">{formData.message}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex items-center justify-between mt-12 pt-8 border-t border-gray-200">
                {currentStep > 1 && (
                  <Button
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                    variant="outline"
                    className="border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white bg-transparent font-bold px-8 py-3"
                  >
                    Previous Step
                  </Button>
                )}

                <div className="flex-1"></div>

                <Button
                  type="submit"
                  className={`font-bold px-8 py-3 ${
                    currentStep === formSteps.length
                      ? "bg-[#159147] hover:bg-[#159147]/90 text-white"
                      : "bg-[#7E2518] hover:bg-[#6B1F14] text-white"
                  }`}
                >
                  {currentStep === formSteps.length ? (
                    <>
                      <Send className="w-5 h-5 mr-2" />
                      Submit Partnership Request
                    </>
                  ) : (
                    "Next Step"
                  )}
                </Button>
              </div>
            </form>
          </div>

          {/* Additional Information */}
          <div className="mt-12 grid md:grid-cols-3 gap-6">
            <div className="bg-[#159147]/10 border border-[#159147]/20 rounded-xl p-6 text-center">
              <Heart className="w-8 h-8 text-[#159147] mx-auto mb-3" />
              <h4 className="font-bold text-[#159147] mb-2">Personalized Matching</h4>
              <p className="text-gray-700 text-sm">
                Our AI-powered system matches you with compatible partners based on your goals and values.
              </p>
            </div>
            <div className="bg-[#7E2518]/10 border border-[#7E2518]/20 rounded-xl p-6 text-center">
              <CheckCircle className="w-8 h-8 text-[#7E2518] mx-auto mb-3" />
              <h4 className="font-bold text-[#7E2518] mb-2">Verified Partners</h4>
              <p className="text-gray-700 text-sm">
                All partners go through our comprehensive verification process to ensure authenticity and credibility.
              </p>
            </div>
            <div className="bg-[#E8B32C]/10 border border-[#E8B32C]/20 rounded-xl p-6 text-center">
              <Building className="w-8 h-8 text-[#C86E36] mx-auto mb-3" />
              <h4 className="font-bold text-[#C86E36] mb-2">Ongoing Support</h4>
              <p className="text-gray-700 text-sm">
                Our team provides continuous support throughout your partnership journey to ensure success.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
