"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight, Quote, TrendingUp, Users } from "lucide-react"

const successStories = [
  {
    id: 1,
    title: "Transforming Traditional Pottery into Global Art",
    investor: "<PERSON>",
    investorRole: "Impact Investment Director",
    knowledgeHolder: "Mama <PERSON>",
    holderRole: "Master Potter",
    location: "Murang'a County",
    investment: "$75,000",
    returns: "340% ROI",
    timeline: "18 months",
    impact: "25 artisans employed",
    story:
      "What started as a small pottery collective has grown into an internationally recognized art cooperative. Traditional Kikuyu pottery techniques are now being taught in art schools worldwide while providing sustainable income to local artisans.",
    image: "/placeholder.svg?height=300&width=400&text=Pottery+Success",
    testimonial:
      "This partnership has not only preserved our traditional pottery methods but has also created opportunities for our youth to learn and earn from their cultural heritage.",
    metrics: [
      { label: "Artisans Trained", value: "45+" },
      { label: "International Orders", value: "200+" },
      { label: "Cultural Workshops", value: "30+" },
    ],
  },
  {
    id: 2,
    title: "Scaling Indigenous Seed Preservation",
    investor: "<PERSON><PERSON> <PERSON>",
    investorRole: "Venture Capital Partner",
    knowledgeHolder: "<PERSON>",
    holderRole: "Traditional Farmer",
    location: "Trans Nzoia County",
    investment: "$120,000",
    returns: "280% ROI",
    timeline: "24 months",
    impact: "500+ farmers network",
    story:
      "A traditional seed preservation initiative has evolved into a comprehensive agricultural program that's helping farmers across East Africa access drought-resistant indigenous crop varieties.",
    image: "/placeholder.svg?height=300&width=400&text=Seeds+Success",
    testimonial:
      "Our ancestors' wisdom about seeds is now helping farmers adapt to climate change. This investment has allowed us to scale our knowledge to help thousands of farmers.",
    metrics: [
      { label: "Seed Varieties", value: "15+" },
      { label: "Farmers Reached", value: "1,200+" },
      { label: "Hectares Planted", value: "3,000+" },
    ],
  },
  {
    id: 3,
    title: "Medicinal Plant Research & Production",
    investor: "East Africa Health Fund",
    investorRole: "Healthcare Investment Fund",
    knowledgeHolder: "Dr. Grace Nyong'o",
    holderRole: "Traditional Healer",
    location: "Kakamega County",
    investment: "$200,000",
    returns: "420% ROI",
    timeline: "30 months",
    impact: "10,000+ patients treated",
    story:
      "Traditional medicinal knowledge has been scientifically validated and scaled into a sustainable healthcare solution that serves both local communities and international markets.",
    image: "/placeholder.svg?height=300&width=400&text=Medicine+Success",
    testimonial:
      "Combining our traditional healing knowledge with modern research has created treatments that are now helping people across Africa access affordable, effective healthcare.",
    metrics: [
      { label: "Medicines Developed", value: "8" },
      { label: "Clinical Trials", value: "12" },
      { label: "Healthcare Centers", value: "25+" },
    ],
  },
]

export default function SuccessStories() {
  const [currentStory, setCurrentStory] = useState(0)

  const nextStory = () => {
    setCurrentStory((prev) => (prev + 1) % successStories.length)
  }

  const prevStory = () => {
    setCurrentStory((prev) => (prev - 1 + successStories.length) % successStories.length)
  }

  const story = successStories[currentStory]

  return (
    <section className="py-20 bg-gradient-to-br from-[#7E2518] via-[#C86E36] to-[#E8B32C] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" fill="none">
          <defs>
            <pattern id="success-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="2" fill="white" />
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5" />
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#success-pattern)" />
        </svg>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Success <span className="text-[#E8B32C]">Stories</span>
            </h2>
            <div className="w-24 h-1 bg-white mx-auto mb-6"></div>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              Real partnerships, real impact. Discover how investors and Indigenous Knowledge holders are creating
              sustainable success together.
            </p>
          </div>

          {/* Story Showcase */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl overflow-hidden">
            <div className="grid lg:grid-cols-2 gap-0">
              {/* Story Image */}
              <div className="relative h-64 lg:h-auto">
                <img src={story.image || "/placeholder.svg"} alt={story.title} className="w-full h-full object-cover" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center text-[#159147]">
                        <TrendingUp className="w-4 h-4 mr-1" />
                        <span className="font-bold">{story.returns}</span>
                      </div>
                      <div className="flex items-center text-[#7E2518]">
                        <Users className="w-4 h-4 mr-1" />
                        <span className="font-bold">{story.impact}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Story Content */}
              <div className="p-8 lg:p-12">
                <div className="mb-6">
                  <h3 className="text-2xl lg:text-3xl font-bold text-[#7E2518] mb-4">{story.title}</h3>
                  <p className="text-gray-700 leading-relaxed mb-6">{story.story}</p>
                </div>

                {/* Partnership Details */}
                <div className="grid md:grid-cols-2 gap-6 mb-6">
                  <div className="bg-[#7E2518]/5 rounded-lg p-4">
                    <h4 className="font-bold text-[#7E2518] mb-2">Investor</h4>
                    <p className="font-semibold text-gray-800">{story.investor}</p>
                    <p className="text-sm text-gray-600">{story.investorRole}</p>
                  </div>
                  <div className="bg-[#159147]/5 rounded-lg p-4">
                    <h4 className="font-bold text-[#159147] mb-2">Knowledge Holder</h4>
                    <p className="font-semibold text-gray-800">{story.knowledgeHolder}</p>
                    <p className="text-sm text-gray-600">{story.holderRole}</p>
                  </div>
                </div>

                {/* Key Metrics */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                  {story.metrics.map((metric, index) => (
                    <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-lg font-bold text-[#7E2518]">{metric.value}</div>
                      <div className="text-xs text-gray-600">{metric.label}</div>
                    </div>
                  ))}
                </div>

                {/* Testimonial */}
                <div className="bg-[#159147]/10 rounded-lg p-4 mb-6">
                  <Quote className="w-6 h-6 text-[#159147] mb-2" />
                  <p className="text-gray-700 italic leading-relaxed">"{story.testimonial}"</p>
                  <p className="text-sm font-semibold text-[#159147] mt-2">- {story.knowledgeHolder}</p>
                </div>

                {/* Investment Details */}
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div>
                    <span className="font-semibold">Investment:</span> {story.investment}
                  </div>
                  <div>
                    <span className="font-semibold">Timeline:</span> {story.timeline}
                  </div>
                  <div>
                    <span className="font-semibold">Location:</span> {story.location}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-center mt-8 space-x-4">
            <button
              onClick={prevStory}
              className="w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
            >
              <ChevronLeft className="w-6 h-6 text-white" />
            </button>

            <div className="flex space-x-2">
              {successStories.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentStory(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentStory ? "bg-white scale-125" : "bg-white/50 hover:bg-white/70"
                  }`}
                />
              ))}
            </div>

            <button
              onClick={nextStory}
              className="w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
            >
              <ChevronRight className="w-6 h-6 text-white" />
            </button>
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center">
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-4">Ready to Create Your Success Story?</h3>
              <p className="text-lg text-white/90 mb-6 max-w-2xl mx-auto">
                Join our community of successful investors and knowledge holders who are making a real difference.
              </p>
              <button className="bg-white text-[#7E2518] font-bold px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors transform hover:scale-105 duration-300">
                Start Your Partnership Journey
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
