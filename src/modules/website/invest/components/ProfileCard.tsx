'use client'

import React from 'react'
import Image from 'next/image'
import { 
  MapPin, 
  Star, 
  Award, 
  Building, 
  TrendingUp, 
  Users,
  Lightbulb
} from 'lucide-react'
import { ProfileCardProps, isInvestorProfile } from '../types'

export default function ProfileCard({ profile, type, onClick }: ProfileCardProps) {
  const isInvestor = isInvestorProfile(profile)

  return (
    <div 
      onClick={onClick}
      className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer overflow-hidden group"
    >
      {/* Profile Image */}
      <div className="relative aspect-square overflow-hidden">
        <Image
          src={profile.image}
          alt={profile.name}
          width={300}
          height={300}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
        />
        {profile.verified && (
          <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
            <Award className="w-3 h-3" />
            Verified
          </div>
        )}
        <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium">
          {profile.badge}
        </div>
        
        {/* Overlay on hover */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <div className="bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium text-gray-900">
            View Details
          </div>
        </div>
      </div>

      {/* Profile Information */}
      <div className="p-6 space-y-4">
        {/* Name and Title */}
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-[#7E2518] transition-colors">
            {profile.name}
          </h3>
          <p className="text-gray-600 text-sm">{profile.title}</p>
        </div>

        {/* Organization and Location */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-gray-600 text-sm">
            <Building className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">{profile.organization}</span>
          </div>
          <div className="flex items-center gap-2 text-gray-600 text-sm">
            <MapPin className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">{profile.location}</span>
          </div>
        </div>

        {/* Focus Area */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-1">
            <Lightbulb className="w-4 h-4 text-yellow-500 flex-shrink-0" />
            <span className="text-sm font-medium text-gray-900">Focus</span>
          </div>
          <p className="text-sm text-gray-700 line-clamp-2">{profile.focus}</p>
        </div>

        {/* Type-specific metrics */}
        <div className="grid grid-cols-2 gap-3">
          {isInvestor ? (
            <>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <TrendingUp className="w-4 h-4 text-blue-500" />
                </div>
                <p className="text-xs text-gray-600 mb-1">Investment</p>
                <p className="text-sm font-semibold text-gray-900">{profile.investment}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Users className="w-4 h-4 text-green-500" />
                </div>
                <p className="text-xs text-gray-600 mb-1">Projects</p>
                <p className="text-sm font-semibold text-gray-900">{profile.projects}</p>
              </div>
            </>
          ) : (
            <>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Award className="w-4 h-4 text-purple-500" />
                </div>
                <p className="text-xs text-gray-600 mb-1">Achievement</p>
                <p className="text-sm font-semibold text-gray-900 line-clamp-1">{profile.achievement}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Users className="w-4 h-4 text-orange-500" />
                </div>
                <p className="text-xs text-gray-600 mb-1">Projects</p>
                <p className="text-sm font-semibold text-gray-900">{profile.projects}</p>
              </div>
            </>
          )}
        </div>

        {/* Rating */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
          <div className="flex items-center gap-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(profile.rating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-sm font-semibold text-gray-900">{profile.rating}</span>
        </div>
      </div>
    </div>
  )
}
