"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { TrendingUp, MapPin, Users, DollarSign, Clock, Star, ArrowRight } from "lucide-react"

const opportunities = [
  {
    id: 1,
    title: "Traditional Honey Production Scaling",
    location: "Baringo County",
    category: "Agriculture",
    investmentNeeded: 150000,
    expectedReturn: "25-35%",
    timeline: "18 months",
    investors: 12,
    funded: 65,
    rating: 4.8,
    description: "Scale traditional beekeeping methods with modern processing facilities",
    highlights: ["Organic certification ready", "Export market access", "Community ownership model"],
    image: "/placeholder.svg?height=200&width=300&text=Honey+Production",
    riskLevel: "Medium",
    impactScore: 9.2,
  },
  {
    id: 2,
    title: "Medicinal Plant Cultivation Hub",
    location: "Meru County",
    category: "Healthcare",
    investmentNeeded: 250000,
    expectedReturn: "30-40%",
    timeline: "24 months",
    investors: 8,
    funded: 45,
    rating: 4.9,
    description: "Sustainable cultivation of indigenous medicinal plants with research backing",
    highlights: ["University partnership", "IP protection secured", "Growing market demand"],
    image: "/placeholder.svg?height=200&width=300&text=Medicinal+Plants",
    riskLevel: "Low",
    impactScore: 9.5,
  },
  {
    id: 3,
    title: "Cultural Tourism Experience Center",
    location: "Maasai Mara",
    category: "Tourism",
    investmentNeeded: 400000,
    expectedReturn: "20-30%",
    timeline: "36 months",
    investors: 15,
    funded: 80,
    rating: 4.7,
    description: "Authentic Maasai cultural experiences with community-led tourism",
    highlights: ["Government support", "International partnerships", "Sustainable tourism model"],
    image: "/placeholder.svg?height=200&width=300&text=Cultural+Tourism",
    riskLevel: "Medium",
    impactScore: 8.8,
  },
]

const categories = ["All", "Agriculture", "Healthcare", "Tourism", "Crafts", "Technology"]

export default function InvestmentOpportunities() {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedOpportunity, setSelectedOpportunity] = useState<number | null>(null)

  const filteredOpportunities =
    selectedCategory === "All" ? opportunities : opportunities.filter((opp) => opp.category === selectedCategory)

  return (
    <section className="py-20 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-[#7E2518]/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#159147]/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-[#7E2518] mb-6">
              Investment <span className="text-[#159147]">Opportunities</span>
            </h2>
            <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover vetted investment opportunities that combine traditional knowledge with modern business models
              for sustainable returns and community impact.
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-3 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                onClick={() => setSelectedCategory(category)}
                variant={selectedCategory === category ? "default" : "outline"}
                className={`px-6 py-2 rounded-full transition-all duration-300 ${
                  selectedCategory === category
                    ? "bg-[#7E2518] text-white shadow-lg"
                    : "border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white"
                }`}
              >
                {category}
              </Button>
            ))}
          </div>

          {/* Opportunities Grid */}
          <div className="grid lg:grid-cols-3 gap-8">
            {filteredOpportunities.map((opportunity) => (
              <div
                key={opportunity.id}
                className="bg-white border border-gray-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden group hover:-translate-y-2"
              >
                {/* Image Header */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={opportunity.image || "/placeholder.svg"}
                    alt={opportunity.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-[#159147] text-white text-xs font-bold px-3 py-1 rounded-full">
                      {opportunity.category}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center">
                      <Star className="w-3 h-3 text-[#E8B32C] fill-current mr-1" />
                      <span className="text-xs font-semibold">{opportunity.rating}</span>
                    </div>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                    <div className="flex items-center text-white text-sm">
                      <MapPin className="w-4 h-4 mr-1" />
                      {opportunity.location}
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-[#7E2518] mb-3 line-clamp-2">{opportunity.title}</h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">{opportunity.description}</p>

                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <DollarSign className="w-5 h-5 text-[#159147] mx-auto mb-1" />
                      <div className="text-lg font-bold text-[#7E2518]">
                        ${(opportunity.investmentNeeded / 1000).toFixed(0)}K
                      </div>
                      <div className="text-xs text-gray-600">Investment Needed</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <TrendingUp className="w-5 h-5 text-[#E8B32C] mx-auto mb-1" />
                      <div className="text-lg font-bold text-[#7E2518]">{opportunity.expectedReturn}</div>
                      <div className="text-xs text-gray-600">Expected Return</div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600">Funding Progress</span>
                      <span className="font-semibold text-[#159147]">{opportunity.funded}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-[#159147] to-[#81B1DB] h-2 rounded-full transition-all duration-500"
                        style={{ width: `${opportunity.funded}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Additional Info */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      {opportunity.investors} investors
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {opportunity.timeline}
                    </div>
                  </div>

                  {/* Highlights */}
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 text-sm mb-2">Key Highlights:</h4>
                    <ul className="space-y-1">
                      {opportunity.highlights.slice(0, 2).map((highlight, index) => (
                        <li key={index} className="text-xs text-gray-600 flex items-center">
                          <div className="w-1.5 h-1.5 bg-[#159147] rounded-full mr-2"></div>
                          {highlight}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action Button */}
                  <Button className="w-full bg-[#7E2518] hover:bg-[#6B1F14] text-white font-semibold">
                    Learn More
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-r from-[#7E2518] to-[#C86E36] rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Ready to Invest in Impact?</h3>
              <p className="text-lg mb-6 opacity-90 max-w-2xl mx-auto">
                Join our platform to access exclusive investment opportunities and connect directly with Indigenous
                Knowledge holders across Kenya.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-white text-[#7E2518] font-bold px-8 py-3 hover:bg-gray-100 transition-colors">
                  View All Opportunities
                </Button>
                <Button
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-[#7E2518] bg-transparent font-bold px-8 py-3"
                >
                  Schedule Consultation
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
