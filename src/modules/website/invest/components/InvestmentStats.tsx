"use client"

import { useState, useEffect } from "react"
import { DollarSign, TrendingUp, Users, Building } from "lucide-react"

const stats = [
  {
    icon: DollarSign,
    value: 25000000,
    suffix: "+",
    label: "Total Investment Potential",
    description: "USD in IKIA opportunities",
    color: "from-[#159147] to-[#81B1DB]",
  },
  {
    icon: TrendingUp,
    value: 85,
    suffix: "%",
    label: "Success Rate",
    description: "Of funded projects thriving",
    color: "from-[#E8B32C] to-[#C86E36]",
  },
  {
    icon: Users,
    value: 1200,
    suffix: "+",
    label: "Active Investors",
    description: "Global investment community",
    color: "from-[#7E2518] to-[#C86E36]",
  },
  {
    icon: Building,
    value: 150,
    suffix: "+",
    label: "IKIA Projects",
    description: "Ready for investment",
    color: "from-[#81B1DB] to-[#159147]",
  },
]

function AnimatedCounter({ value, duration = 2000 }: { value: number; duration?: number }) {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)

      setCount(Math.floor(progress * value))

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)
    return () => cancelAnimationFrame(animationFrame)
  }, [value, duration])

  return <span>{count.toLocaleString()}</span>
}

export default function InvestmentStats() {
  return (
    <section className="py-20 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-gray-50 via-white to-gray-50"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-[#E8B32C]/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-[#159147]/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-[#7E2518] mb-6">
              Investment Impact
              <span className="text-[#159147]"> at Scale</span>
            </h2>
            <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our platform has facilitated meaningful connections between investors and Indigenous Knowledge holders,
              creating sustainable economic opportunities while preserving cultural heritage.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="group relative bg-white rounded-2xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
              >
                {/* Background Gradient */}
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5 rounded-2xl group-hover:opacity-10 transition-opacity`}
                ></div>

                {/* Icon */}
                <div
                  className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${stat.color} rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300`}
                >
                  <stat.icon className="w-8 h-8 text-white" />
                </div>

                {/* Value */}
                <div className="mb-4">
                  <div className="text-4xl font-bold text-[#7E2518] mb-2">
                    {stat.value >= 1000000 ? (
                      <>
                        $<AnimatedCounter value={stat.value / 1000000} />M
                      </>
                    ) : (
                      <>
                        <AnimatedCounter value={stat.value} />
                        {stat.suffix}
                      </>
                    )}
                  </div>
                  <h3 className="text-lg font-bold text-gray-800 mb-2">{stat.label}</h3>
                  <p className="text-gray-600 text-sm">{stat.description}</p>
                </div>

                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div
                    className={`h-full bg-gradient-to-r ${stat.color} rounded-full transition-all duration-1000 delay-500`}
                    style={{
                      width: `${Math.min((stat.value / stats.reduce((max, s) => Math.max(max, s.value), 0)) * 100, 100)}%`,
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-r from-[#7E2518] to-[#C86E36] rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Ready to Make an Impact?</h3>
              <p className="text-lg mb-6 opacity-90">
                Join our community of forward-thinking investors and be part of preserving Indigenous wisdom while
                generating sustainable returns.
              </p>
              <button className="bg-white text-[#7E2518] font-bold px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors transform hover:scale-105 duration-300">
                Start Investing Today
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
