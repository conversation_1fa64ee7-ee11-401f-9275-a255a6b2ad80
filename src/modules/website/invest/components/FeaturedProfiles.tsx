'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { ExternalLink, User, Building, Lightbulb, MapPin, Star, Award } from 'lucide-react'
import Image from 'next/image'

const profileTypes = [
  { id: 'investors', label: 'INVESTORS', icon: Building, color: 'bg-[#7E2518]' },
  { id: 'exhibitors', label: 'EXHIBITORS', icon: User, color: 'bg-[#159147]' },
]

const profileData = {
  investors: {
    title: 'Featured Investor Bio',
    profiles: [
      {
        name: '<PERSON>',
        title: 'Impact Investment Director',
        organization: 'Kenya Development Fund',
        location: 'Nairobi, Kenya',
        focus: 'Sustainable Agriculture & Traditional Medicine',
        investment: '$2.5M+ invested',
        projects: '12 active projects',
        rating: 4.9,
        image: '/placeholder.svg?height=200&width=200&text=<PERSON>+<PERSON>',
        badge: 'Top Investor',
        verified: true,
      },
      {
        name: 'Dr. <PERSON>',
        title: 'Venture Capital Partner',
        organization: 'East Africa Growth Partners',
        location: 'Kisumu, Kenya',
        focus: 'Technology & Indigenous Knowledge Integration',
        investment: '$5.2M+ portfolio',
        projects: '8 successful exits',
        rating: 4.8,
        image: '/placeholder.svg?height=200&width=200&text=Dr.+<PERSON>+Ochieng',
        badge: 'Verified',
        verified: true,
      },
    ],
  },
  exhibitors: {
    title: 'Featured Exhibitor Bio',
    profiles: [
      {
        name: 'Mama Grace Wanjiku',
        title: 'Traditional Medicine Practitioner',
        organization: 'Kikuyu Herbal Collective',
        location: "Murang'a, Kenya",
        focus: 'Medicinal Plants & Traditional Healing',
        achievement: '30+ years experience',
        projects: '500+ patients helped',
        rating: 4.9,
        image: '/placeholder.svg?height=200&width=200&text=Mama+Grace',
        badge: 'Master Healer',
        verified: true,
      },
      {
        name: 'Joseph Kiplagat',
        title: 'Master Craftsman',
        organization: 'Kalenjin Traditional Arts',
        location: 'Eldoret, Kenya',
        focus: 'Traditional Pottery & Cultural Artifacts',
        achievement: 'UNESCO Heritage Recognition',
        projects: '200+ artworks created',
        rating: 4.7,
        image: '/placeholder.svg?height=200&width=200&text=Joseph+Kiplagat',
        badge: 'UNESCO Recognized',
        verified: true,
      },
    ],
  },
}

export default function FeaturedProfiles() {
  const [activeTab, setActiveTab] = useState('investors')

  const currentData = profileData[activeTab as keyof typeof profileData]
  const activeType = profileTypes.find((type) => type.id === activeTab)

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-[#E8B32C]/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#159147]/10 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-[#7E2518] mb-6">
              Meet Our <span className="text-[#159147]">Community</span>
            </h2>
            <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Connect with verified investors, authentic knowledge holders, and innovative
              entrepreneurs who are shaping the future of Indigenous Knowledge assets.
            </p>
          </div>

          {/* Enhanced Tab Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mb-16">
            {profileTypes.map((type) => (
              <Button
                key={type.id}
                onClick={() => setActiveTab(type.id)}
                className={`px-8 py-4 font-bold text-lg rounded-xl transition-all duration-300 transform hover:scale-105 ${
                  activeTab === type.id
                    ? `${type.color} text-white shadow-2xl`
                    : 'bg-white text-[#7E2518] border-2 border-[#7E2518] hover:bg-[#7E2518] hover:text-white shadow-lg'
                }`}
              >
                <type.icon className="w-5 h-5 mr-3" />
                {type.label}
              </Button>
            ))}
          </div>

          {/* Profile Cards Grid */}
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Featured Profile Card */}
            <div className="lg:col-span-1">
              <div
                className={`${activeType?.color} text-white rounded-2xl shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-300`}
              >
                <div className="p-8 text-center">
                  <div className="w-24 h-24 bg-white/20 rounded-full mx-auto mb-6 flex items-center justify-center">
                    {activeType && <activeType.icon className="w-12 h-12 text-white" />}
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{currentData.title}</h3>
                  <p className="text-white/90 mb-6 leading-relaxed">
                    Discover verified profiles of leading {activeTab} in the IKIA ecosystem. Each
                    profile is carefully vetted for authenticity and impact.
                  </p>
                  <Button
                    variant="outline"
                    className="border-2 border-white text-white hover:bg-white hover:text-[#7E2518] bg-transparent font-bold"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View All Profiles
                  </Button>
                </div>
              </div>
            </div>

            {/* Individual Profile Cards */}
            <div className="lg:col-span-2 grid md:grid-cols-2 gap-6">
              {currentData.profiles.map((profile, index) => (
                <div
                  key={index}
                  className="bg-white border border-gray-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden group hover:-translate-y-2"
                >
                  {/* Profile Header */}
                  <div className="relative p-6 pb-4">
                    {/* Badge */}
                    <div className="absolute top-4 right-4">
                      <span
                        className={`${activeType?.color} text-white text-xs font-bold px-3 py-1 rounded-full`}
                      >
                        {profile.badge}
                      </span>
                    </div>

                    {/* Profile Image & Basic Info */}
                    <div className="flex items-start space-x-4">
                      <div className="relative">
                        <Image
                          src={profile.image || '/placeholder.svg'}
                          alt={profile.name}
                          width={64}
                          height={64}
                          className="w-16 h-16 rounded-xl object-cover border-2 border-gray-200"
                        />
                        {profile.verified && (
                          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-[#159147] rounded-full flex items-center justify-center">
                            <Award className="w-3 h-3 text-white" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold text-[#7E2518] mb-1">{profile.name}</h4>
                        <p className="text-[#159147] font-semibold text-sm mb-1">{profile.title}</p>
                        <p className="text-gray-600 text-sm">{profile.organization}</p>
                      </div>
                    </div>

                    {/* Location & Rating */}
                    <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                      <div className="flex items-center text-gray-500 text-sm">
                        <MapPin className="w-4 h-4 mr-1" />
                        {profile.location}
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-[#E8B32C] fill-current mr-1" />
                        <span className="text-sm font-semibold text-gray-700">
                          {profile.rating}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Profile Details */}
                  <div className="px-6 pb-6">
                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="font-semibold text-gray-700">Focus Area:</span>
                        <p className="text-gray-600 mt-1">{profile.focus}</p>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <span className="font-semibold text-gray-700">
                            {activeTab === 'investors' ? 'Investment:' : 'Achievement:'}
                          </span>
                          <p className="text-[#C86E36] font-semibold mt-1">
                            {activeTab === 'investors' ? profile.investment : profile.achievement}
                          </p>
                        </div>
                        <div>
                          <span className="font-semibold text-gray-700">Projects:</span>
                          <p className="text-[#159147] font-semibold mt-1">{profile.projects}</p>
                        </div>
                      </div>
                    </div>

                    <Button
                      size="sm"
                      className={`w-full mt-6 ${activeType?.color} hover:opacity-90 text-white font-semibold`}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Full Profile
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
