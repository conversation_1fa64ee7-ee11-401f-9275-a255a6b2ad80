'use client'

const partners = [
  {
    name: 'Republic of Kenya',
    logo: '/Court of arms.png',
    description: 'Government of Kenya',
  },
  {
    name: 'National Museum of Kenya',
    logo: '/National Museums of Kenya.png',
    description: 'Cultural Heritage Institution',
  },
  {
    name: 'National Policy Institute',
    logo: '/NPI.png',
    description: 'Policy Research & Development',
  },
  {
    name: 'Kenya Vision 2030',
    logo: '/Vision 2030.png',
    description: 'National Development Blueprint',
  },
  {
    name: 'Council of Governors',
    logo: '/Council of Governors.png',
    description: 'County Government Leadership',
  },
]

export default function PartnersSection() {
  return (
    <section className="py-20 bg-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 border-2 border-[#7E2518] transform rotate-45"></div>
        <div className="absolute bottom-10 right-10 w-24 h-24 border-2 border-[#159147] transform rotate-12"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-[#E8B32C] transform -rotate-12"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">

        {/* Premium Partners Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-10 items-center justify-items-center">
          {partners.map((partner, index) => (
            <div key={index} className="group relative">
              {/* Outer Decorative Border */}
              <div className="absolute -inset-3 border-2 border-[#E8B32C] opacity-30 transition-all duration-500 rounded-lg"></div>

              {/* Middle Border */}
              <div className="absolute -inset-1 border border-[#C86E36] opacity-20 transition-all duration-300 rounded-lg"></div>

              {/* Main Square Container */}
              <div className="relative bg-white shadow-xl transition-all duration-300 border-4 border-white overflow-hidden rounded-lg w-[180px] h-[180px]">
                {/* Logo Display */}
                <div className="absolute inset-0 flex items-center justify-center p-6">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={partner.logo || '/placeholder.svg'}
                    alt={partner.name}
                    className="max-w-full max-h-full object-contain"
                    style={{
                      maxWidth: '85%',
                      maxHeight: '85%',
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src =
                        '/placeholder.svg?height=100&width=100&text=' +
                        encodeURIComponent(partner.name)
                    }}
                  />
                </div>
              </div>

              {/* Partner Name Below */}
              <div className="mt-4 text-center">
                <h3 className="font-bold text-sm text-gray-800 leading-tight">
                  {partner.name}
                </h3>
                <p className="text-xs text-gray-600 mt-1">
                  {partner.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
