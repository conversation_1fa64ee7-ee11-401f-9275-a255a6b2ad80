import { TrendingUp, Globe, Users, Award } from 'lucide-react'

export default function InvestHero() {
  return (
    <section className="relative bg-gradient-to-br from-[#7E2518] via-[#C86E36] to-[#E8B32C] py-20 lg:py-32 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" fill="none">
          <defs>
            <pattern id="investment-grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="1" fill="white" />
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5" />
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#investment-grid)" />
        </svg>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-16 h-16 bg-white/10 rounded-full animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-12 h-12 bg-white/10 rounded-full animate-bounce"></div>
      <div className="absolute top-1/2 left-1/4 w-8 h-8 bg-white/10 rounded-full animate-ping"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            {/* Premium Badge */}
            <div className="inline-flex items-center bg-white/20 backdrop-blur-sm border border-white/30 rounded-full px-6 py-3 mb-8">
              <Award className="w-5 h-5 text-white mr-2" />
              <span className="text-white font-semibold">Premium Investment Platform</span>
              <div className="w-2 h-2 bg-[#159147] rounded-full ml-3 animate-pulse"></div>
            </div>

            {/* Main Title */}
            <h1 className="font-acquire text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight">
              INVESTOR &
              <br />
              <span className="text-[#E8B32C]">ENTREPRENEUR</span>
              <br />
              HUB
            </h1>

            {/* Subtitle with Enhanced Design */}
            <div className="bg-white/95 backdrop-blur-md border-4 border-white shadow-2xl p-8 max-w-3xl mx-auto mb-12 transform hover:scale-105 transition-transform duration-300">
              <p className="text-2xl md:text-3xl text-[#7E2518] font-bold leading-relaxed">
                Invest in Indigenous Knowledge
                <br />
                <span className="text-[#159147]">Intellectual Assets</span>
              </p>
              <div className="w-24 h-1 bg-[#E8B32C] mx-auto mt-4"></div>
            </div>

            {/* Value Proposition */}
            <div className="max-w-4xl mx-auto mb-12">
              <p className="text-xl text-white/90 leading-relaxed font-medium">
                Unlock the potential of traditional wisdom through strategic investments. Connect
                with authentic Indigenous Knowledge holders and discover sustainable opportunities
                that create lasting impact across Kenya's diverse communities.
              </p>
            </div>

            {/* Key Features */}
            <div className="grid md:grid-cols-4 gap-6 max-w-5xl mx-auto">
              {[
                {
                  icon: TrendingUp,
                  title: 'High Impact Returns',
                  desc: 'Sustainable growth opportunities',
                },
                { icon: Globe, title: '47 Counties', desc: 'Nationwide coverage' },
                { icon: Users, title: 'Verified Partners', desc: 'Trusted knowledge holders' },
                { icon: Award, title: 'Legal Protection', desc: 'IP rights secured' },
              ].map((feature, index) => (
                <div
                  key={index}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 hover:bg-white/20 transition-all duration-300 group"
                >
                  <feature.icon className="w-8 h-8 text-[#E8B32C] mx-auto mb-3 group-hover:scale-110 transition-transform" />
                  <h3 className="text-white font-bold text-lg mb-2">{feature.title}</h3>
                  <p className="text-white/80 text-sm">{feature.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" fill="none" className="w-full h-auto">
          <path
            d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z"
            fill="white"
            className="drop-shadow-lg"
          />
        </svg>
      </div>
    </section>
  )
}
