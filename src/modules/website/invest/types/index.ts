// Invest Module Types

export interface BaseProfile {
  name: string
  title: string
  organization: string
  location: string
  focus: string
  rating: number
  image: string
  badge: string
  verified: boolean
}

export interface InvestorProfile extends BaseProfile {
  investment: string
  projects: string
}

export interface ExhibitorProfile extends BaseProfile {
  achievement: string
  projects: string
}

export interface ProfileData {
  investors: {
    title: string
    profiles: InvestorProfile[]
  }
  exhibitors: {
    title: string
    profiles: ExhibitorProfile[]
  }
}

export interface ProfileModalProps {
  profile: InvestorProfile | ExhibitorProfile
  isOpen: boolean
  onClose: () => void
  type: 'investor' | 'exhibitor'
}

export interface ProfileCardProps {
  profile: InvestorProfile | ExhibitorProfile
  type: 'investor' | 'exhibitor'
  onClick: () => void
}

export interface ProfileGridProps {
  profiles: InvestorProfile[] | ExhibitorProfile[]
  type: 'investor' | 'exhibitor'
  title: string
}

// Type guards
export const isInvestorProfile = (profile: InvestorProfile | ExhibitorProfile): profile is InvestorProfile => {
  return 'investment' in profile
}

export const isExhibitorProfile = (profile: InvestorProfile | ExhibitorProfile): profile is ExhibitorProfile => {
  return 'achievement' in profile
}
