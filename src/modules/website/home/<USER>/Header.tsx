"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { navigationItems } from "../data";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="w-full bg-white border-b-2 border-primary">
      <div className="max-w-7xl mx-auto px-6">
        {/* Main Header */}
        <div className="flex items-center justify-between h-20">
          {/* Logo Section */}
          <div className="flex items-center">
            <Link
              href="/"
              className="flex items-center hover:opacity-90 transition-opacity duration-200"
            >
              <div className="relative h-10 sm:h-12 w-auto">
                <Image
                  src="/assets/iconography/logo-horizontal-no-bg.png"
                  alt="IKIA Conference 2025 Logo"
                  width={200}
                  height={48}
                  className="h-10 sm:h-12 w-auto object-contain max-w-[150px] sm:max-w-[200px]"
                  priority
                />
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <Link
                key={item.title}
                href={item.href}
                className="font-myriad font-medium text-foreground hover:text-primary transition-colors"
              >
                {item.title}
              </Link>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="flex items-center space-x-4">
            <button className="bg-secondary text-secondary-foreground font-myriad font-semibold px-6 py-2 rounded-sm hover:bg-secondary/90 hover:shadow-md transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2">
              Register Now
            </button>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <div className="w-6 h-6 flex flex-col justify-center space-y-1">
                <span className="w-full h-0.5 bg-foreground"></span>
                <span className="w-full h-0.5 bg-foreground"></span>
                <span className="w-full h-0.5 bg-foreground"></span>
              </div>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <nav className="flex flex-col space-y-4">
              {navigationItems.map((item) => (
                <Link
                  key={item.title}
                  href={item.href}
                  className="font-myriad font-medium text-foreground hover:text-primary transition-colors py-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.title}
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
