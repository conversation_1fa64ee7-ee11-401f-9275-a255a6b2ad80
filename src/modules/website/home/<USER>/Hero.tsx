import { conferenceInfo, conferenceDate } from '../data'
import CountdownCard from './CountdownCard'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowRight, Users, Calendar } from 'lucide-react'

export default function Hero() {
  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* Background Image with Overlays */}
      <div className="absolute inset-0">
        {/* Background Video */}
        <video
          autoPlay
          muted
          loop
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
        >
          <source src="/landing-page-video.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Black Overlay with opacity */}
        <div className="absolute inset-0 bg-black/80" />

        {/* Decorative Pattern Overlay */}
        {/* <div className="absolute inset-0 opacity-10">
          <svg className="w-full h-full" viewBox="0 0 100 100" fill="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#7E2518" strokeWidth="0.5" />
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div> */}
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-16">
        {/* Main Hero Container */}
        <div className="flex flex-col items-center justify-center min-h-[70vh] gap-8">
          {/* Centered CTA Content */}
          <div className="flex flex-col space-y-8 max-w-2xl text-center">
            {/* Enhanced Badge */}
            <div className="flex justify-center">
              <span className="bg-[#E8B32C] text-[#7E2518] font-bold px-3 py-1 text-xs shadow-lg border border-[#7E2518]/20 flex items-center space-x-1 transform -skew-x-12 uppercase">
                <span className="text-lg">🇰🇪</span>
                <span>IKIA Conference & Trade Fair 2025</span>
                <div className="w-2 h-2 bg-[#159147] rounded-full animate-pulse"></div>
              </span>
            </div>

            {/* Enhanced Main Title */}
            <div className="flex flex-col space-y-4">
              <h1 className="font-myriad font-semibold text-2xl lg:text-3xl xl:text-4xl text-white leading-tight capitalize">
                {conferenceInfo.title}
              </h1>
              {/* Subtitle removed as requested */}
              {/* Description card removed as requested */}
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/registration">
                <button className="bg-[#7E2518] hover:bg-[#6B1F14] text-white font-medium px-6 py-3 rounded-lg text-base transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center space-x-2 border-2 border-transparent">
                  <span>Register Now</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 7l5 5m0 0l-5 5m5-5H6"
                    />
                  </svg>
                </button>
              </Link>
              <Link href="/about">
                <button className="bg-white border-2 border-[#7E2518] hover:bg-[#7E2518] text-[#7E2518] hover:text-white font-medium px-6 py-3 rounded-lg text-base transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center space-x-2">
                  <span>Learn More</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 7l5 5m0 0l-5 5m5-5H6"
                    />
                  </svg>
                </button>
              </Link>
            </div>

            {/* Scroll Indicator */}
            <div className="flex flex-col items-center space-y-2 animate-bounce mt-8">
              <span className="text-white text-sm font-medium">Scroll Down</span>
              <svg
                className="w-6 h-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 14l-7 7m0 0l-7-7m7 7V3"
                />
              </svg>
            </div>
          </div>

          {/* Right image section removed */}
        </div>
      </div>

      {/* Old scroll indicator removed - now inline with content */}

      {/* Bottom Decorative Line */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#E8B32C] via-[#C86E36] to-[#159147]"></div>
    </section>
  )
}
