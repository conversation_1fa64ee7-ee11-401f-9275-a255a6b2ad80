'use client'

import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { ArrowRight, Play, TrendingUp, Handshake } from 'lucide-react'
import { useState } from 'react'

export default function ExhibitionsSection() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="font-acquire font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4">
            INVESTMENT OPPORTUNITIES
          </h2>
          <h3 className="font-acquire font-bold text-3xl lg:text-4xl text-primary mb-6">
            Discover Exhibition Investments
          </h3>
          <p className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Explore viable investment opportunities in indigenous knowledge assets and traditional
            products through our comprehensive trade fair exhibitions. Connect with innovative
            projects ready for commercial partnership.
          </p>
        </div>

        {/* Call-to-Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
          <Link href="/exhibitions">
            <Button
              size="lg"
              className="w-full sm:w-auto px-8 py-4 text-lg font-semibold bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] group"
            >
              <TrendingUp className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
              Explore Investments
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>

          <Link href="/registration/form/investor">
            <Button
              size="lg"
              className="w-full sm:w-auto px-8 py-4 text-lg font-semibold bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] group"
            >
              <TrendingUp className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
              Investment Interest
            </Button>
          </Link>

          <Link href="/registration/form/exhibitor">
            <Button
              variant="outline"
              size="lg"
              className="w-full sm:w-auto px-8 py-4 text-lg font-semibold border-2 border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground transition-all duration-300 group"
            >
              <Handshake className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
              Register Exhibition
            </Button>
          </Link>
        </div>

        {/* Video Section */}
        <div className="relative max-w-5xl mx-auto">
          <div className="relative bg-muted/30 rounded-2xl overflow-hidden border-2 border-muted shadow-2xl">
            {/* Video Container */}
            <div className="relative aspect-video bg-gradient-to-br from-primary/20 to-secondary/20">
              {!isVideoPlaying ? (
                // Video Thumbnail/Placeholder
                <div className="absolute inset-0 flex flex-col items-center justify-center space-y-6 bg-gradient-to-br from-primary/10 to-secondary/10">
                  {/* Play Button */}
                  <button
                    onClick={() => setIsVideoPlaying(true)}
                    className="w-24 h-24 bg-white/90 hover:bg-white rounded-full flex items-center justify-center shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 group"
                  >
                    <Play
                      className="w-10 h-10 text-primary ml-1 group-hover:text-secondary transition-colors"
                      fill="currentColor"
                    />
                  </button>

                  {/* Video Title */}
                  <div className="text-center space-y-2">
                    <h4 className="font-acquire font-bold text-xl text-primary">
                      IKIA Investment Showcase
                    </h4>
                    <p className="font-myriad text-muted-foreground max-w-md">
                      Watch how indigenous knowledge assets are transforming into sustainable
                      investment opportunities
                    </p>
                  </div>

                  {/* Decorative Elements */}
                  <div className="absolute top-8 left-8 w-16 h-16 bg-secondary/20 rounded-full animate-pulse"></div>
                  <div className="absolute bottom-8 right-8 w-12 h-12 bg-accent/20 rounded-full animate-bounce-slow"></div>
                  <div className="absolute top-1/3 right-16 w-8 h-8 bg-primary/20 rounded-full animate-pulse animation-delay-2000"></div>
                </div>
              ) : (
                // Actual Video
                <video
                  autoPlay
                  controls
                  className="w-full h-full object-cover"
                  onEnded={() => setIsVideoPlaying(false)}
                >
                  <source src="/exhibitions-showcase.mp4" type="video/mp4" />
                  <div className="absolute inset-0 flex items-center justify-center bg-muted">
                    <p className="font-myriad text-muted-foreground">
                      Video not available. Please check back later.
                    </p>
                  </div>
                </video>
              )}
            </div>

            {/* Video Caption */}
            <div className="p-6 bg-background border-t border-muted">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div>
                  <h5 className="font-acquire font-bold text-lg text-primary mb-2">
                    Investment Conference Preview
                  </h5>
                  <p className="font-myriad text-sm text-muted-foreground">
                    Get an inside look at the investment opportunities and exhibition highlights at
                    the IKIA Investment Conference.
                  </p>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
                  <span className="font-myriad">Live Preview</span>
                </div>
              </div>
            </div>
          </div>

          {/* Video Stats */}
          <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted/20 rounded-lg border border-muted">
              <div className="font-acquire font-bold text-2xl text-primary">50+</div>
              <div className="font-myriad text-sm text-muted-foreground">Exhibition Booths</div>
            </div>
            <div className="text-center p-4 bg-muted/20 rounded-lg border border-muted">
              <div className="font-acquire font-bold text-2xl text-secondary">Ksh10M+</div>
              <div className="font-myriad text-sm text-muted-foreground">Investment Ready</div>
            </div>
            <div className="text-center p-4 bg-muted/20 rounded-lg border border-muted">
              <div className="font-acquire font-bold text-2xl text-accent">100+</div>
              <div className="font-myriad text-sm text-muted-foreground">Investor Partners</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
