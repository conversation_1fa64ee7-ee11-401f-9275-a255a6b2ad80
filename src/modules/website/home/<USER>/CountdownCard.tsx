'use client'

import { useState, useEffect } from 'react'

interface CountdownCardProps {
  targetDate: Date
  className?: string
}

interface TimeLeft {
  days: number
  hours: number
  minutes: number
  seconds: number
}

export default function CountdownCard({ targetDate, className = '' }: CountdownCardProps) {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime()
      const distance = targetDate.getTime() - now

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000),
        })
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [targetDate])

  return (
    <div
      className={`bg-white/90 backdrop-blur-md rounded-2xl p-6 border border-gray-200 shadow-xl ${className}`}
    >
      <div className="text-center mb-4">
        <h3 className="text-[#7E2518] font-bold text-lg mb-2">Conference Starts In</h3>
        <p className="text-[#C86E36] text-sm font-medium">November 19, 2025 • 9:00 AM</p>
      </div>

      <div className="grid grid-cols-4 gap-3">
        <div className="text-center">
          <div className="bg-[#7E2518] text-white rounded-lg p-3 mb-2">
            <div className="text-2xl font-bold">{timeLeft.days}</div>
          </div>
          <div className="text-gray-600 text-xs font-medium">Days</div>
        </div>

        <div className="text-center">
          <div className="bg-[#7E2518] text-white rounded-lg p-3 mb-2">
            <div className="text-2xl font-bold">{timeLeft.hours}</div>
          </div>
          <div className="text-gray-600 text-xs font-medium">Hours</div>
        </div>

        <div className="text-center">
          <div className="bg-[#7E2518] text-white rounded-lg p-3 mb-2">
            <div className="text-2xl font-bold">{timeLeft.minutes}</div>
          </div>
          <div className="text-gray-600 text-xs font-medium">Minutes</div>
        </div>

        <div className="text-center">
          <div className="bg-[#7E2518] text-white rounded-lg p-3 mb-2">
            <div className="text-2xl font-bold">{timeLeft.seconds}</div>
          </div>
          <div className="text-gray-600 text-xs font-medium">Seconds</div>
        </div>
      </div>

      <div className="mt-4 text-center">
        <div className="flex items-center justify-center space-x-2">
          <div className="w-2 h-2 bg-[#159147] rounded-full animate-pulse"></div>
          <span className="text-gray-600 text-sm">Live Registration Open</span>
          <div className="w-2 h-2 bg-[#159147] rounded-full animate-pulse"></div>
        </div>
      </div>
    </div>
  )
}
