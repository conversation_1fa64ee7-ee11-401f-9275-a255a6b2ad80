import Image from 'next/image'

const sponsors = {
  platinum: [
    {
      name: 'Kenya Commercial Bank',
      logo: '/placeholder.svg?height=100&width=200&text=KCB',
      website: '#',
    },
    {
      name: 'Safaricom Foundation',
      logo: '/placeholder.svg?height=100&width=200&text=Safaricom',
      website: '#',
    },
  ],
  gold: [
    { name: 'Equity Bank', logo: '/placeholder.svg?height=80&width=160&text=Equity', website: '#' },
    { name: 'USAID Kenya', logo: '/placeholder.svg?height=80&width=160&text=USAID', website: '#' },
    {
      name: 'World Bank',
      logo: '/placeholder.svg?height=80&width=160&text=World+Bank',
      website: '#',
    },
  ],
  silver: [
    {
      name: 'Co-operative Bank',
      logo: '/placeholder.svg?height=60&width=120&text=Co-op',
      website: '#',
    },
    { name: 'UNDP Kenya', logo: '/placeholder.svg?height=60&width=120&text=UNDP', website: '#' },
    { name: 'GIZ Kenya', logo: '/placeholder.svg?height=60&width=120&text=GIZ', website: '#' },
    {
      name: 'Ford Foundation',
      logo: '/placeholder.svg?height=60&width=120&text=Ford',
      website: '#',
    },
  ],
}

export default function SponsorsTab() {
  return (
    <div className="space-y-12">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-[#7E2518] mb-4">Event Sponsors</h3>
        <p className="text-gray-600 max-w-2xl mx-auto">
          We are grateful to our sponsors who make this event possible and support the advancement
          of Indigenous Knowledge and Innovation Assets.
        </p>
      </div>

      {/* Platinum Sponsors */}
      <div>
        <h4 className="text-xl font-bold text-[#7E2518] mb-6 text-center">Platinum Sponsors</h4>
        <div className="grid md:grid-cols-2 gap-8 justify-items-center">
          {sponsors.platinum.map((sponsor, index) => (
            <a
              key={index}
              href={sponsor.website}
              className="block bg-white border-2 border-[#E8B32C] p-8 hover:shadow-lg transition-shadow"
            >
              <Image
                src={sponsor.logo || '/placeholder.svg'}
                alt={sponsor.name}
                className="h-20 mx-auto object-contain"
              />
            </a>
          ))}
        </div>
      </div>

      {/* Gold Sponsors */}
      <div>
        <h4 className="text-xl font-bold text-[#7E2518] mb-6 text-center">Gold Sponsors</h4>
        <div className="grid md:grid-cols-3 gap-6 justify-items-center">
          {sponsors.gold.map((sponsor, index) => (
            <a
              key={index}
              href={sponsor.website}
              className="block bg-white border border-[#C86E36] p-6 hover:shadow-lg transition-shadow"
            >
              <Image
                src={sponsor.logo || '/placeholder.svg'}
                alt={sponsor.name}
                className="h-16 mx-auto object-contain"
              />
            </a>
          ))}
        </div>
      </div>

      {/* Silver Sponsors */}
      <div>
        <h4 className="text-xl font-bold text-[#7E2518] mb-6 text-center">Silver Sponsors</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 justify-items-center">
          {sponsors.silver.map((sponsor, index) => (
            <a
              key={index}
              href={sponsor.website}
              className="block bg-white border border-gray-300 p-4 hover:shadow-lg transition-shadow"
            >
              <Image
                src={sponsor.logo || '/placeholder.svg'}
                alt={sponsor.name}
                className="h-12 mx-auto object-contain"
              />
            </a>
          ))}
        </div>
      </div>

      {/* Become a Sponsor */}
      <div className="bg-[#7E2518]/5 border border-[#7E2518]/20 p-8 text-center">
        <h4 className="text-xl font-bold text-[#7E2518] mb-4">Become a Sponsor</h4>
        <p className="text-gray-700 mb-6 max-w-2xl mx-auto">
          Join our mission to promote Indigenous Knowledge and Innovation Assets. Partner with us to
          make a meaningful impact on sustainable development.
        </p>
        <button className="bg-[#7E2518] text-white px-8 py-3 font-medium hover:bg-[#6B1F14] transition-colors">
          View Sponsorship Packages
        </button>
      </div>
    </div>
  )
}
