'use client'

import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, Users, Building, Globe } from 'lucide-react'
import Link from 'next/link'

export default function AllPartnersHero() {
  return (
    <section className="relative py-20 lg:py-28 overflow-hidden">
      {/* Background with clip-path */}
      <div
        className="absolute inset-0 bg-[#7E2518]"
        style={{ clipPath: 'polygon(0 0, 65% 0, 45% 100%, 0 100%)' }}
      />
      <div className="absolute inset-0 bg-white" />

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="lg:pr-8">
            {/* Breadcrumb */}
            <div className="inline-flex items-center gap-2 bg-[#7E2518]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
              <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
              <span
                className="text-sm font-medium text-[#7E2518]"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                All Partners
              </span>
            </div>

            <h1
              className="text-4xl lg:text-6xl font-bold mb-6 leading-tight text-[#7E2518]"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Our Strategic
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#E8B32C] to-[#C86E36]">
                Partners
              </span>
            </h1>

            <div className="w-24 h-1 bg-[#E8B32C] rounded-full mb-6"></div>

            <p
              className="text-xl mb-8 text-gray-700 max-w-lg leading-relaxed"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Discover all the organizations, institutions, and communities collaborating with us to advance indigenous knowledge preservation and commercialization.
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                asChild
                className="bg-[#7E2518] hover:bg-[#159147] text-white border-0 rounded-xl font-bold transition-all duration-500 hover:shadow-lg transform hover:-translate-y-1 px-8"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                <Link href="/partners-sponsors" className="flex items-center gap-2">
                  <ArrowLeft className="w-5 h-5" />
                  Back to Partners & Sponsors
                </Link>
              </Button>
            </div>
          </div>

          {/* Right Content - Stats */}
          <div className="lg:pl-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 text-center">
                <div className="w-12 h-12 bg-[#7E2518] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Building className="w-6 h-6 text-white" />
                </div>
                <h3
                  className="text-2xl font-bold text-[#7E2518] mb-2"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  50+
                </h3>
                <p
                  className="text-gray-600 text-sm"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Strategic Partners
                </p>
              </div>

              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 text-center">
                <div className="w-12 h-12 bg-[#159147] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h3
                  className="text-2xl font-bold text-[#7E2518] mb-2"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  25+
                </h3>
                <p
                  className="text-gray-600 text-sm"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Community Partners
                </p>
              </div>

              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 text-center sm:col-span-2">
                <div className="w-12 h-12 bg-[#E8B32C] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Globe className="w-6 h-6 text-white" />
                </div>
                <h3
                  className="text-2xl font-bold text-[#7E2518] mb-2"
                  style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                >
                  15+
                </h3>
                <p
                  className="text-gray-600 text-sm"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Countries Represented
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
