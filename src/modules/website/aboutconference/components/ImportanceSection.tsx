'use client'

import { Card, CardContent } from '@/components/ui/card'
import {
  Users,
  Target,
  TrendingUp,
  Globe,
  CheckCircle,
} from 'lucide-react'
import { IconType } from 'react-icons'

interface ImportancePoint {
  title: string
  description: string
  icon: IconType
}

export default function ImportanceSection() {
  const colors = ['#7E2518', '#159147', '#E8B32C', '#C86E36', '#81B1DB']

  const importancePoints: ImportancePoint[] = [
    {
      icon: Globe,
      title: 'Global Recognition',
      description:
        'IKIAs represent a vital part of cultural heritage with significant ecological and economic value for sustainable development.',
    },
    {
      icon: TrendingUp,
      title: 'Economic Development',
      description:
        'They play a key role across sectors including health, cosmetics, agriculture, and eco-tourism, driving innovation.',
    },
    {
      icon: Target,
      title: 'Strategic Innovation',
      description:
        'Transforming traditional knowledge and natural resources helps protect biodiversity and promote sustainable practices.',
    },
    {
      icon: Users,
      title: 'Community Empowerment',
      description:
        'They empower local communities by generating ownership, innovation, and sustainable livelihoods for future generations.',
    },
    {
      icon: CheckCircle,
      title: 'Policy Support',
      description:
        'IKIAs support inclusive and sustainable development by bridging cultural identity with modern innovation frameworks.',
    },
  ]

  return (
    <section className="py-16 lg:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2
            className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4"
            style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
          >
            Importance of IKIAs
          </h2>
          <p
            className="text-gray-600 max-w-2xl mx-auto"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Understanding the critical role of Indigenous Knowledge and Intellectual Assets in
            sustainable development
          </p>
        </div>

        <div className="relative max-w-5xl mx-auto">
          <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-[#7E2518] via-[#E8B32C] to-[#159147] rounded-full hidden lg:block" />

          {importancePoints.map((point, index) => {
            const Icon = point.icon
            const isEven = index % 2 === 0
            const bgColor = colors[index % colors.length]

            return (
              <div
                key={index}
                className={`relative flex items-center mb-12 ${
                  isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } flex-col lg:gap-0 gap-6`}
              >
                <div className={`w-full lg:w-5/12 ${isEven ? 'lg:pr-8' : 'lg:pl-8'}`}>
                  <Card className="bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4 mb-4">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: bgColor }}
                        >
                          <Icon className="w-6 h-6 text-white" />
                        </div>
                        <h3
                          className="font-bold text-[#7E2518] text-lg"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          {point.title}
                        </h3>
                      </div>
                      <p
                        className="text-gray-600 leading-relaxed"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {point.description}
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <div
                  className="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full border-4 border-white shadow-lg hidden lg:block"
                  style={{ backgroundColor: bgColor }}
                ></div>

                <div className="w-full lg:w-5/12 hidden lg:block"></div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
