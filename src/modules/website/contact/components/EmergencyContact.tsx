import { Phone, Shield, Users, AlertTriangle } from 'lucide-react'

const emergencyContacts = [
  {
    icon: AlertTriangle,
    title: 'Medical Emergency',
    number: '+254 700 000 000',
    description: '24/7 Medical assistance',
  },
  {
    icon: Shield,
    title: 'Security',
    number: '+254 701 000 000',
    description: 'Event security team',
  },
  {
    icon: Users,
    title: 'Event Staff',
    number: '+254 702 000 000',
    description: 'General event support',
  },
]

export default function EmergencyContact() {
  return (
    <section className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-[#7E2518] mb-4">Emergency Contact Information</h2>
        <p className="text-gray-600">Important contacts during the event</p>
      </div>

      <div className="bg-gradient-to-r from-[#7E2518]/5 to-[#C86E36]/5 rounded-2xl p-8 border border-[#7E2518]/10">
        <div className="grid md:grid-cols-3 gap-6">
          {emergencyContacts.map((contact, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center"
            >
              <contact.icon className="w-8 h-8 text-[#7E2518] mx-auto mb-3" />
              <h3 className="font-bold text-[#7E2518] mb-2">{contact.title}</h3>
              <a
                href={`tel:${contact.number}`}
                className="text-xl font-bold text-[#159147] hover:text-[#159147]/80 transition-colors block mb-2"
              >
                {contact.number}
              </a>
              <p className="text-sm text-gray-600">{contact.description}</p>
            </div>
          ))}
        </div>
        <div className="mt-6 text-center">
          <p className="text-[#7E2518] font-medium">
            <Phone className="w-4 h-4 inline mr-2" />
            Save these numbers in your phone before the event
          </p>
        </div>
      </div>
    </section>
  )
}
