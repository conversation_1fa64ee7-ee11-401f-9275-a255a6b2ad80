'use client'

import { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'

const faqs = [
  {
    question: 'How can I register?',
    answer:
      'You can register for IKIA events through our online registration portal. Visit the registration page, fill out the required information, and complete the payment process. Early bird discounts are available for registrations made 30 days in advance.',
  },
  {
    question: 'Are accommodations available?',
    answer:
      'Yes, we have partnered with several hotels and lodges in the area to provide accommodation options for attendees. Booking information and special rates are available on our accommodations page. We recommend booking early as spaces fill up quickly during event periods.',
  },
  {
    question: 'What should I bring to the event?',
    answer:
      "Please bring a valid ID, your registration confirmation, business cards for networking, and any materials relevant to your participation. We'll provide welcome packets with additional information upon arrival.",
  },
  {
    question: 'Is parking available at the venue?',
    answer:
      'Yes, Thika Greens Golf Resort provides ample parking space for all attendees. Parking is complimentary for registered participants. VIP parking is available for premium ticket holders.',
  },
  {
    question: "Can I get a refund if I can't attend?",
    answer:
      'Refund policies vary depending on the type of registration and timing of cancellation. Please refer to our terms and conditions or contact our support team for specific refund requests.',
  },
]

export default function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems((prev) =>
      prev.includes(index) ? prev.filter((item) => item !== index) : [...prev, index],
    )
  }

  return (
    <section className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-[#7E2518] mb-4">Frequently Asked Questions</h2>
        <p className="text-gray-600">Find answers to common questions about IKIA events</p>
      </div>

      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="divide-y divide-gray-200">
          {faqs.map((faq, index) => (
            <div key={index} className="p-6">
              <button
                onClick={() => toggleItem(index)}
                className="w-full flex items-center justify-between text-left focus:outline-none group"
              >
                <h3 className="text-lg font-semibold text-[#7E2518] group-hover:text-[#C86E36] transition-colors">
                  {faq.question}
                </h3>
                {openItems.includes(index) ? (
                  <ChevronUp className="w-5 h-5 text-[#7E2518] flex-shrink-0" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-[#7E2518] flex-shrink-0" />
                )}
              </button>
              {openItems.includes(index) && (
                <div className="mt-4 text-gray-600 leading-relaxed">{faq.answer}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
