'use client'

import type React from 'react'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Send } from 'lucide-react'

export default function ContactForm() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    subject: '',
    message: '',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
  }

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  return (
    <section className="max-w-4xl mx-auto">
      <div className="bg-white shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-[#7E2518] p-6 text-white">
          <h2 className="text-2xl font-bold">Contact Form</h2>
          <p className="text-[#E8B32C] mt-2">We&apos;d love to hear from you</p>
        </div>

        <form onSubmit={handleSubmit} className="p-8 space-y-6">
          {/* Full Name */}
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-[#7E2518] mb-2">
              Full Name *
            </label>
            <input
              type="text"
              id="fullName"
              name="fullName"
              required
              value={formData.fullName}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
              placeholder="Enter your full name"
            />
          </div>

          {/* Email Address */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-[#7E2518] mb-2">
              Email Address *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              value={formData.email}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
              placeholder="Enter your email address"
            />
          </div>

          {/* Subject */}
          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-[#7E2518] mb-2">
              Subject
            </label>
            <select
              id="subject"
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
            >
              <option value="">Select a subject</option>
              <option value="general">General Inquiry</option>
              <option value="exhibition">Exhibition Information</option>
              <option value="investment">Investment Opportunities</option>
              <option value="partnership">Partnership</option>
              <option value="media">Media Inquiry</option>
            </select>
          </div>

          {/* Message */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-[#7E2518] mb-2">
              Message *
            </label>
            <textarea
              id="message"
              name="message"
              required
              rows={6}
              value={formData.message}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors resize-none"
              placeholder="Enter your message here..."
            />
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full bg-[#7E2518] hover:bg-[#6B1F14] text-white py-3 text-lg font-medium"
          >
            <Send className="w-5 h-5 mr-2" />
            Send Message
          </Button>
        </form>
      </div>
    </section>
  )
}
