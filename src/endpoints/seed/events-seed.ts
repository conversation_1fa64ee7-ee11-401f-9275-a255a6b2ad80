import type { Payload } from 'payload'

export const seedEvents = async (payload: Payload) => {
  console.log('Seeding events...')

  // First, create some speakers
  const speaker1 = await payload.create({
    collection: 'speakers',
    data: {
      name: 'Dr. <PERSON>',
      title: 'Chief Technology Officer',
      company: 'TechCorp Inc.',
      bio: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Dr. <PERSON> is a renowned expert in artificial intelligence and machine learning with over 15 years of experience in the tech industry.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      slug: 'dr-sarah-johnson',
    },
  })

  const speaker2 = await payload.create({
    collection: 'speakers',
    data: {
      name: '<PERSON>',
      title: 'Senior Software Engineer',
      company: 'DevSolutions',
      bio: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: '<PERSON> is a full-stack developer specializing in React, Node.js, and cloud architecture.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      slug: 'michael-chen',
    },
  })

  const speaker3 = await payload.create({
    collection: 'speakers',
    data: {
      name: 'Emily Rodriguez',
      title: 'UX Design Lead',
      company: 'DesignStudio',
      bio: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Emily Rodriguez is a user experience designer with a passion for creating intuitive and accessible digital products.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      slug: 'emily-rodriguez',
    },
  })

  // Create events
  const events = [
    {
      title: 'Opening Keynote: The Future of AI',
      description: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Join us for an inspiring keynote about the future of artificial intelligence and its impact on society.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      type: 'keynote',
      date: '2024-03-15',
      startTime: '09:00',
      endTime: '10:00',
      day: 1,
      location: 'Main Auditorium',
      speakers: [speaker1.id],
    },
    {
      title: 'Building Scalable Web Applications',
      description: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Learn best practices for building scalable web applications using modern frameworks and cloud technologies.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      type: 'workshop',
      date: '2024-03-15',
      startTime: '10:30',
      endTime: '12:00',
      day: 1,
      location: 'Workshop Room A',
      speakers: [speaker2.id],
    },
    {
      title: 'Design Systems Panel Discussion',
      description: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'A panel discussion on creating and maintaining effective design systems in large organizations.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      type: 'panel',
      date: '2024-03-15',
      startTime: '14:00',
      endTime: '15:30',
      day: 1,
      location: 'Conference Room B',
      speakers: [speaker3.id, speaker1.id],
    },
    {
      title: 'Advanced React Patterns',
      description: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Deep dive into advanced React patterns including hooks, context, and performance optimization.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      type: 'workshop',
      date: '2024-03-16',
      startTime: '09:30',
      endTime: '11:00',
      day: 2,
      location: 'Workshop Room B',
      speakers: [speaker2.id],
    },
    {
      title: 'Tech Innovation Exhibition',
      description: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Explore the latest innovations in technology from leading companies and startups.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      type: 'exhibition',
      date: '2024-03-16',
      startTime: '11:30',
      endTime: '17:00',
      day: 2,
      location: 'Exhibition Hall',
      speakers: [],
    },
  ]

  // Create events
  for (const eventData of events) {
    await payload.create({
      collection: 'events',
      data: eventData,
    })
  }

  console.log('Events seeded successfully!')
}
