import type { PayloadRequest } from 'payload'

interface TransformedCounty {
  id: string
  name: string
  code: string
  coordinates: {
    latitude: number
    longitude: number
  }
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface CountiesResponse {
  counties: TransformedCounty[]
  totalCounties: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Calculate distance between two coordinates using Haversine formula
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371 // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLon = (lon2 - lon1) * Math.PI / 180
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

// Transform county data
const transformCounty = (county: any): TransformedCounty => {
  return {
    id: county.id,
    name: county.name,
    code: county.code,
    coordinates: {
      latitude: county.coordinates?.latitude,
      longitude: county.coordinates?.longitude,
    },
    description: county.description,
    isActive: county.isActive,
    createdAt: county.createdAt,
    updatedAt: county.updatedAt,
  }
}

export const countiesHandler = async (req: PayloadRequest, res: any): Promise<void> => {
  try {
    const { payload } = req
    
    // Parse query parameters
    const {
      name,
      code,
      active,
      near_lat,
      near_lng,
      radius,
      limit = '50',
      page = '1',
      sort = 'name'
    } = req.query as Record<string, string>

    // Build where clause
    const where: any = {}

    if (name) where.name = { contains: name }
    if (code) where.code = { equals: code }
    if (active !== undefined) where.isActive = { equals: active === 'true' }

    // Fetch counties
    const countiesResult = await payload.find({
      collection: 'counties',
      where,
      limit: parseInt(limit),
      page: parseInt(page),
      sort: sort as any,
      depth: 1,
    })

    // Transform counties
    let transformedCounties: TransformedCounty[] = countiesResult.docs.map(transformCounty)

    // Apply proximity filter if coordinates provided
    if (near_lat && near_lng) {
      const lat = parseFloat(near_lat)
      const lng = parseFloat(near_lng)
      const maxRadius = radius ? parseFloat(radius) : 100 // Default 100km radius

      transformedCounties = transformedCounties
        .map(county => ({
          ...county,
          distance: calculateDistance(lat, lng, county.coordinates.latitude, county.coordinates.longitude)
        }))
        .filter(county => county.distance <= maxRadius)
        .sort((a, b) => a.distance - b.distance)
    }

    const currentPage = parseInt(page)
    const currentLimit = parseInt(limit)
    const totalPages = Math.ceil(countiesResult.totalDocs / currentLimit)

    const response: CountiesResponse = {
      counties: transformedCounties,
      totalCounties: countiesResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in counties endpoint:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}

// Get single county by ID or code
export const countyByIdHandler = async (req: PayloadRequest, res: any): Promise<void> => {
  try {
    const { payload } = req
    const { id } = req.query as Record<string, string>

    if (!id) {
      res.status(400).json({ error: 'County ID or code is required' })
      return
    }

    // Try to find by ID first, then by code
    let county
    try {
      county = await payload.findByID({
        collection: 'counties',
        id: parseInt(id),
        depth: 1,
      })
    } catch {
      // If ID lookup fails, try by code
      const result = await payload.find({
        collection: 'counties',
        where: { code: { equals: id } },
        limit: 1,
        depth: 1,
      })
      county = result.docs[0]
    }

    if (!county) {
      res.status(404).json({ error: 'County not found' })
      return
    }

    const transformedCounty = transformCounty(county)
    res.status(200).json({ county: transformedCounty })
  } catch (error) {
    console.error('Error in county by ID endpoint:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}

// Get counties within a bounding box
export const countiesInBoundsHandler = async (req: PayloadRequest, res: any): Promise<void> => {
  try {
    const { payload } = req
    const { north, south, east, west } = req.query as Record<string, string>

    if (!north || !south || !east || !west) {
      res.status(400).json({
        error: 'Bounding box coordinates required: north, south, east, west'
      })
      return
    }

    const northLat = parseFloat(north)
    const southLat = parseFloat(south)
    const eastLng = parseFloat(east)
    const westLng = parseFloat(west)

    // Fetch all active counties (we'll filter by coordinates in memory)
    const countiesResult = await payload.find({
      collection: 'counties',
      where: { isActive: { equals: true } },
      limit: 1000, // Large limit to get all counties
      depth: 1,
    })

    // Filter counties within bounding box
    const countiesInBounds = countiesResult.docs
      .filter((county: any) => {
        const lat = county.coordinates?.latitude
        const lng = county.coordinates?.longitude

        if (!lat || !lng) return false

        return lat >= southLat && lat <= northLat && lng >= westLng && lng <= eastLng
      })
      .map(transformCounty)

    res.status(200).json({
      counties: countiesInBounds,
      totalCounties: countiesInBounds.length,
      bounds: { north: northLat, south: southLat, east: eastLng, west: westLng }
    })
  } catch (error) {
    console.error('Error in counties in bounds endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}

// Create a new county
export const createCountyHandler = async (req: PayloadRequest, res: any): Promise<void> => {
  try {
    const { payload, user } = req

    // Check if user is authenticated
    if (!user) {
      res.status(401).json({ error: 'Authentication required' })
      return
    }

    const { name, code, coordinates, description, isActive = true } = req.body as any

    // Validate required fields
    if (!name || !code || !coordinates?.latitude || !coordinates?.longitude) {
      res.status(400).json({
        error: 'Missing required fields: name, code, coordinates (latitude, longitude)'
      })
      return
    }

    // Validate coordinates
    if (coordinates.latitude < -90 || coordinates.latitude > 90) {
      res.status(400).json({
        error: 'Latitude must be between -90 and 90 degrees'
      })
      return
    }

    if (coordinates.longitude < -180 || coordinates.longitude > 180) {
      res.status(400).json({
        error: 'Longitude must be between -180 and 180 degrees'
      })
      return
    }

    // Create the county
    const newCounty = await payload.create({
      collection: 'counties',
      data: {
        name,
        code,
        coordinates: {
          latitude: parseFloat(coordinates.latitude),
          longitude: parseFloat(coordinates.longitude),
        },
        description,
        isActive,
      },
      req,
    })

    const transformedCounty = transformCounty(newCounty)
    res.status(201).json({
      message: 'County created successfully',
      county: transformedCounty
    })
  } catch (error: any) {
    console.error('Error creating county:', error)

    // Handle duplicate code error
    if (error.message?.includes('duplicate') || error.code === 11000) {
      res.status(409).json({
        error: 'County code already exists',
        message: 'A county with this code already exists'
      })
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}

// Update a county
export const updateCountyHandler = async (req: PayloadRequest, res: any): Promise<void> => {
  try {
    const { payload, user } = req
    const { id } = req.query as Record<string, string>

    // Check if user is authenticated
    if (!user) {
      res.status(401).json({ error: 'Authentication required' })
      return
    }

    if (!id) {
      res.status(400).json({ error: 'County ID is required' })
      return
    }

    const { name, code, coordinates, description, isActive } = req.body as any

    // Build update data (only include provided fields)
    const updateData: any = {}
    if (name !== undefined) updateData.name = name
    if (code !== undefined) updateData.code = code
    if (description !== undefined) updateData.description = description
    if (isActive !== undefined) updateData.isActive = isActive

    // Handle coordinates update
    if (coordinates) {
      if (coordinates.latitude !== undefined || coordinates.longitude !== undefined) {
        updateData.coordinates = {}

        if (coordinates.latitude !== undefined) {
          if (coordinates.latitude < -90 || coordinates.latitude > 90) {
            res.status(400).json({
              error: 'Latitude must be between -90 and 90 degrees'
            })
            return
          }
          updateData.coordinates.latitude = parseFloat(coordinates.latitude)
        }

        if (coordinates.longitude !== undefined) {
          if (coordinates.longitude < -180 || coordinates.longitude > 180) {
            res.status(400).json({
              error: 'Longitude must be between -180 and 180 degrees'
            })
            return
          }
          updateData.coordinates.longitude = parseFloat(coordinates.longitude)
        }
      }
    }

    // Update the county
    const updatedCounty = await payload.update({
      collection: 'counties',
      id: parseInt(id),
      data: updateData,
      req,
    })

    const transformedCounty = transformCounty(updatedCounty)
    res.status(200).json({
      message: 'County updated successfully',
      county: transformedCounty
    })
  } catch (error: any) {
    console.error('Error updating county:', error)

    if (error.message?.includes('No document found')) {
      res.status(404).json({ error: 'County not found' })
    } else if (error.message?.includes('duplicate') || error.code === 11000) {
      res.status(409).json({
        error: 'County code already exists',
        message: 'A county with this code already exists'
      })
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}

// Delete a county
export const deleteCountyHandler = async (req: PayloadRequest, res: any): Promise<void> => {
  try {
    const { payload, user } = req
    const { id } = req.query as Record<string, string>

    // Check if user is authenticated
    if (!user) {
      res.status(401).json({ error: 'Authentication required' })
      return
    }

    if (!id) {
      res.status(400).json({ error: 'County ID is required' })
      return
    }

    // Delete the county
    await payload.delete({
      collection: 'counties',
      id: parseInt(id),
      req,
    })

    res.status(200).json({
      message: 'County deleted successfully',
      id: parseInt(id)
    })
  } catch (error: any) {
    console.error('Error deleting county:', error)

    if (error.message?.includes('No document found')) {
      res.status(404).json({ error: 'County not found' })
    } else {
      res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}

// Get users in a specific county
export const countyUsersHandler = async (req: PayloadRequest, res: any): Promise<void> => {
  try {
    const { payload } = req
    const { id } = req.query as Record<string, string>
    const { limit = '50', page = '1', sort = 'name' } = req.query as Record<string, string>

    if (!id) {
      res.status(400).json({ error: 'County ID is required' })
      return
    }

    // First, verify the county exists
    let county
    try {
      county = await payload.findByID({
        collection: 'counties',
        id: parseInt(id),
        depth: 0,
      })
    } catch {
      res.status(404).json({ error: 'County not found' })
      return
    }

    // Get users in this county
    const usersResult = await payload.find({
      collection: 'users',
      where: {
        county: { equals: parseInt(id) }
      },
      depth: 1, // Include county data
      limit: parseInt(limit),
      page: parseInt(page),
      sort: sort as any,
    })

    // Transform user data (remove sensitive fields like password)
    const transformedUsers = usersResult.docs.map((user: any) => ({
      id: user.id,
      name: user.name,
      email: user.email,
      county: user.county ? {
        id: user.county.id,
        name: user.county.name,
        code: user.county.code,
      } : null,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }))

    const currentPage = parseInt(page)
    const currentLimit = parseInt(limit)
    const totalPages = Math.ceil(usersResult.totalDocs / currentLimit)

    res.status(200).json({
      county: {
        id: county.id,
        name: county.name,
        code: county.code,
      },
      users: transformedUsers,
      totalUsers: usersResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    })
  } catch (error) {
    console.error('Error in county users endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}


