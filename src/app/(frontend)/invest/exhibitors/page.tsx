'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Search, Filter, User, Award, Users, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import ProfileCard from '@/modules/website/invest/components/ProfileCard'
import ProfileModal from '@/modules/website/invest/components/ProfileModal'
import { exhibitorProfiles } from '@/modules/website/invest/data/profiles'
import { ExhibitorProfile } from '@/modules/website/invest/types'

export default function AllExhibitorsPage() {
  const [selectedProfile, setSelectedProfile] = useState<ExhibitorProfile | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterBy, setFilterBy] = useState('all')

  const handleProfileClick = (profile: ExhibitorProfile) => {
    setSelectedProfile(profile)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedProfile(null)
  }

  const filteredProfiles = exhibitorProfiles.filter(profile => {
    const matchesSearch = profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         profile.organization.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         profile.focus.toLowerCase().includes(searchTerm.toLowerCase())
    
    if (filterBy === 'all') return matchesSearch
    if (filterBy === 'top-rated') return matchesSearch && profile.rating >= 4.8
    if (filterBy === 'verified') return matchesSearch && profile.verified
    
    return matchesSearch
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-[#159147] to-[#81B1DB] text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            {/* Back Navigation */}
            <div className="flex justify-start mb-8">
              <Link href="/invest">
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-[#159147]">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Invest
                </Button>
              </Link>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              All <span className="text-[#E8B32C]">Exhibitors</span>
            </h1>
            <div className="w-24 h-1 bg-white mx-auto mb-6"></div>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              Discover verified Indigenous Knowledge holders and cultural practitioners who are preserving and sharing traditional wisdom.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <div className="bg-white/10 backdrop-blur-md rounded-lg p-6">
                <div className="flex items-center justify-center mb-2">
                  <User className="w-8 h-8 text-[#E8B32C]" />
                </div>
                <div className="text-2xl font-bold">{exhibitorProfiles.length}+</div>
                <div className="text-white/80">Knowledge Holders</div>
              </div>
              <div className="bg-white/10 backdrop-blur-md rounded-lg p-6">
                <div className="flex items-center justify-center mb-2">
                  <Award className="w-8 h-8 text-[#E8B32C]" />
                </div>
                <div className="text-2xl font-bold">500+</div>
                <div className="text-white/80">Years of Experience</div>
              </div>
              <div className="bg-white/10 backdrop-blur-md rounded-lg p-6">
                <div className="flex items-center justify-center mb-2">
                  <Users className="w-8 h-8 text-[#E8B32C]" />
                </div>
                <div className="text-2xl font-bold">50+</div>
                <div className="text-white/80">Cultural Practices</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search exhibitors by name, organization, or focus area..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#159147] focus:border-transparent"
                />
              </div>

              {/* Filter */}
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#159147] focus:border-transparent"
                >
                  <option value="all">All Exhibitors</option>
                  <option value="top-rated">Top Rated (4.8+)</option>
                  <option value="verified">Verified Only</option>
                </select>
              </div>
            </div>

            {/* Results Count */}
            <div className="mt-4 text-gray-600">
              Showing {filteredProfiles.length} of {exhibitorProfiles.length} exhibitors
            </div>
          </div>

          {/* Exhibitors Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProfiles.map((profile, index) => (
              <ProfileCard
                key={index}
                profile={profile}
                type="exhibitor"
                onClick={() => handleProfileClick(profile)}
              />
            ))}
          </div>

          {/* No Results */}
          {filteredProfiles.length === 0 && (
            <div className="text-center py-16">
              <div className="text-gray-400 mb-4">
                <Users className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No exhibitors found</h3>
              <p className="text-gray-500">Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>
      </div>

      {/* Profile Modal */}
      {selectedProfile && (
        <ProfileModal
          profile={selectedProfile}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          type="exhibitor"
        />
      )}
    </div>
  )
}
