'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Download,
  ExternalLink,
  FileText,
  Users,
  Building,
  UserPlus,
  BookOpen,
  ClipboardList,
  Phone,
  Mail,
  MapPin,
  ArrowRight,
} from 'lucide-react'

export default function ResourcesPage() {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  const downloadResources = [
    {
      id: 1,
      title: 'Ikia Investment Guide',
      description:
        'Comprehensive guide for investment opportunities in indigenous knowledge assets',
      icon: BookOpen,
      action: 'Download',
      href: '/downloads/ikia-investment-guide.pdf',
      color: 'from-[#159147] to-[#0f7a3a]',
      hoverColor: 'hover:shadow-[#159147]/25',
    },
    {
      id: 2,
      title: 'Sponsorship Package',
      description: 'Detailed sponsorship opportunities and benefits for conference partners',
      icon: Building,
      action: 'Download',
      href: '/downloads/sponsorship-package.pdf',
      color: 'from-[#7E2518] to-[#5a1a10]',
      hoverColor: 'hover:shadow-[#7E2518]/25',
    },
    {
      id: 3,
      title: 'Conference Program',
      description: 'Complete agenda, speakers, and session details for the conference',
      icon: ClipboardList,
      action: 'Download',
      href: '/downloads/conference-program.pdf',
      color: 'from-[#E8B32C] to-[#d19d1f]',
      hoverColor: 'hover:shadow-[#E8B32C]/25',
    },
    {
      id: 4,
      title: 'Investor Guide',
      description: 'Essential information for investors interested in IKIA opportunities',
      icon: FileText,
      action: 'Download',
      href: '/downloads/investor-guide.pdf',
      color: 'from-[#C86E36] to-[#a85a2b]',
      hoverColor: 'hover:shadow-[#C86E36]/25',
    },
    {
      id: 5,
      title: 'Ikia Holder Guide',
      description: 'Guidelines and resources for indigenous knowledge asset holders',
      icon: Users,
      action: 'Download',
      href: '/downloads/ikia-holder-guide.pdf',
      color: 'from-[#81B1DB] to-[#6a9bc7]',
      hoverColor: 'hover:shadow-[#81B1DB]/25',
    },
  ]

  const registrationLinks = [
    {
      id: 6,
      title: 'Register Delegate/VIP',
      description: 'Register as a conference delegate or VIP attendee',
      icon: UserPlus,
      action: 'Register',
      href: '/register/delegate',
      color: 'from-[#159147] to-[#0f7a3a]',
      hoverColor: 'hover:shadow-[#159147]/25',
    },
    {
      id: 7,
      title: 'Register as Exhibitor',
      description: 'Join as an exhibitor to showcase your products and services',
      icon: Building,
      action: 'Register',
      href: '/register/exhibitor',
      color: 'from-[#7E2518] to-[#5a1a10]',
      hoverColor: 'hover:shadow-[#7E2518]/25',
    },
    {
      id: 8,
      title: 'Register as Sponsor',
      description: 'Become a conference sponsor and support indigenous knowledge development',
      icon: Building,
      action: 'Register',
      href: '/register/sponsor',
      color: 'from-[#E8B32C] to-[#d19d1f]',
      hoverColor: 'hover:shadow-[#E8B32C]/25',
    },
    {
      id: 9,
      title: 'Investor Interest Form',
      description: 'Express your interest in investing in indigenous knowledge assets',
      icon: ExternalLink,
      action: 'Register',
      href: '/forms/investor-interest',
      color: 'from-[#C86E36] to-[#a85a2b]',
      hoverColor: 'hover:shadow-[#C86E36]/25',
    },
  ]

  const allResources = [...downloadResources, ...registrationLinks]

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[#7E2518]/5 via-white to-[#159147]/5 py-16 lg:py-24 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -left-40 w-60 h-60 bg-[#E8B32C]/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -right-40 w-72 h-72 bg-[#81B1DB]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            {/* Conference Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#7E2518]/8 to-[#159147]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
              <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
              <span
                className="text-sm font-medium text-[#7E2518]"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Quick Access Resources
              </span>
            </div>

            {/* Main Heading */}
            <h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#7E2518] mb-6 leading-tight"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Investment Conference
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#7E2518] to-[#159147]">
                Resources
              </span>
            </h1>

            <div className="w-24 h-1 bg-gradient-to-r from-[#E8B32C] to-[#C86E36] rounded-full mx-auto mb-6"></div>

            <p
              className="text-lg md:text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Access essential documents, registration forms, and resources for the IKIA Investment
              Conference. Download guides, register for participation, or express your interest in
              indigenous knowledge investment opportunities.
            </p>
          </div>
        </div>
      </section>

      {/* Resources Grid Section */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
            {allResources.map((resource) => {
              const IconComponent = resource.icon
              const isHovered = hoveredCard === resource.id

              return (
                <Card
                  key={resource.id}
                  className={`group relative bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden ${resource.hoverColor}`}
                  onMouseEnter={() => setHoveredCard(resource.id)}
                  onMouseLeave={() => setHoveredCard(null)}
                >
                  {/* Background Gradient Overlay */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${resource.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                  ></div>

                  {/* Animated Border Glow */}
                  {isHovered && (
                    <div className="absolute -inset-1 bg-gradient-to-r from-[#159147]/20 via-[#E8B32C]/20 to-[#7E2518]/20 rounded-lg blur-sm animate-pulse"></div>
                  )}

                  <CardContent className="relative p-8 h-full flex flex-col">
                    {/* Icon and Action Badge */}
                    <div className="flex items-center justify-between mb-6">
                      <div
                        className={`p-4 bg-gradient-to-br ${resource.color} rounded-xl shadow-lg transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 relative overflow-hidden`}
                      >
                        <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <IconComponent className="w-8 h-8 text-white relative z-10" />
                      </div>

                      <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-600 group-hover:bg-gray-200 transition-colors duration-300">
                        {resource.action === 'Download' ? (
                          <Download className="w-3 h-3" />
                        ) : (
                          <ExternalLink className="w-3 h-3" />
                        )}
                        <span style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}>
                          {resource.action}
                        </span>
                      </div>
                    </div>

                    {/* Title */}
                    <h3
                      className="text-xl font-bold text-[#7E2518] mb-3 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-[#7E2518] group-hover:to-[#159147] transition-all duration-500"
                      style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                    >
                      {resource.title}
                    </h3>

                    {/* Description */}
                    <p
                      className="text-gray-600 leading-relaxed flex-grow group-hover:text-gray-700 transition-colors duration-300 text-sm"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {resource.description}
                    </p>

                    {/* Action Button */}
                    <div className="mt-6">
                      <Button
                        asChild
                        className={`w-full bg-gradient-to-r ${resource.color} hover:shadow-lg hover:shadow-current/25 text-white border-0 rounded-lg font-semibold tracking-wide transition-all duration-500 transform group-hover:scale-105 group-hover:-translate-y-1`}
                        style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                      >
                        <a href={resource.href} className="flex items-center justify-center gap-2">
                          {resource.action === 'Download' ? (
                            <Download className="w-4 h-4" />
                          ) : (
                            <ExternalLink className="w-4 h-4" />
                          )}
                          {resource.action}
                          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                        </a>
                      </Button>
                    </div>

                    {/* Progress Bar Animation */}
                    <div className="mt-4 h-1 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className={`h-full bg-gradient-to-r ${resource.color} rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out`}
                      ></div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2
                className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Need Help or Have Questions?
              </h2>
              <p
                className="text-lg text-gray-700 leading-relaxed"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Our team is here to assist you with any questions about the conference resources or
                registration process.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Contact Information */}
              <Card className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8">
                  <h3
                    className="text-xl font-bold text-[#7E2518] mb-6"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    Get in Touch
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-[#159147] rounded-full flex items-center justify-center">
                        <Phone className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p
                          className="font-medium text-[#7E2518]"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          Call or WhatsApp
                        </p>
                        <p
                          className="text-gray-600"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          +254 70 233 555
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-[#E8B32C] rounded-full flex items-center justify-center">
                        <Mail className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p
                          className="font-medium text-[#7E2518]"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          Email Us
                        </p>
                        <p
                          className="text-gray-600"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          <EMAIL>
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-[#7E2518] rounded-full flex items-center justify-center">
                        <MapPin className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p
                          className="font-medium text-[#7E2518]"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          Visit Us
                        </p>
                        <p
                          className="text-gray-600"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Nairobi, Kenya
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Links */}
              <Card className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8">
                  <h3
                    className="text-xl font-bold text-[#7E2518] mb-6"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    Quick Links
                  </h3>

                  <div className="space-y-3">
                    {[
                      { name: 'About Conference', href: '/about' },
                      { name: 'FAQ', href: '/faq' },
                      { name: 'Terms & Conditions', href: '/terms' },
                      { name: 'Contact Support', href: '/contact' },
                    ].map((link, index) => (
                      <a
                        key={index}
                        href={link.href}
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-300 group"
                      >
                        <span
                          className="text-gray-700 group-hover:text-[#7E2518] transition-colors duration-300"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {link.name}
                        </span>
                        <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-[#7E2518] group-hover:translate-x-1 transition-all duration-300" />
                      </a>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
