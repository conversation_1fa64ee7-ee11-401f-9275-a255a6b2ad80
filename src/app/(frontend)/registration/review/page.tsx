"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ArrowLeft, CheckCircle, CreditCard, FileText, Shield } from "lucide-react"

export default function RegistrationReview() {
  const router = useRouter()
  const [agreements, setAgreements] = useState({
    terms: false,
    privacy: false,
    marketing: false,
  })

  // Mock data - in real app, this would come from form state/context
  const registrationData = {
    type: "Conference Delegate",
    package: "Standard Package",
    price: "KES 25,000",
    personalInfo: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+254 700 000 000",
      organization: "Example University",
      country: "Kenya",
    },
    professionalInfo: {
      thematicAreas: ["Traditional Foods & Nutrition", "Indigenous Technologies & Innovations"],
      experience: "6-10 years",
    },
  }

  const handleAgreementChange = (field: string, checked: boolean) => {
    setAgreements(prev => ({
      ...prev,
      [field]: checked
    }))
  }

  const canProceed = agreements.terms && agreements.privacy

  const handleSubmit = () => {
    if (canProceed) {
      // Here you would process the payment and complete registration
      router.push("/registration/success")
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Form
        </Button>
        
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
            <FileText className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Review Your Registration
            </h1>
            <p className="text-blue-600 font-semibold">Please review your information before proceeding to payment</p>
          </div>
        </div>
      </div>

      <div className="space-y-8">
        {/* Registration Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center space-x-2" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              <CheckCircle className="w-6 h-6 text-green-600" />
              <span>Registration Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Registration Type</h3>
                <p className="text-gray-700">{registrationData.type}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Selected Package</h3>
                <p className="text-gray-700">{registrationData.package}</p>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-gray-900">Total Amount</span>
                <span className="text-2xl font-bold text-green-600">{registrationData.price}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Full Name</h3>
                <p className="text-gray-700">{registrationData.personalInfo.name}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Email</h3>
                <p className="text-gray-700">{registrationData.personalInfo.email}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Phone</h3>
                <p className="text-gray-700">{registrationData.personalInfo.phone}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Organization</h3>
                <p className="text-gray-700">{registrationData.personalInfo.organization}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Country</h3>
                <p className="text-gray-700">{registrationData.personalInfo.country}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Experience</h3>
                <p className="text-gray-700">{registrationData.professionalInfo.experience}</p>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Areas of Interest</h3>
              <div className="flex flex-wrap gap-2">
                {registrationData.professionalInfo.thematicAreas.map((area, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                  >
                    {area}
                  </span>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Terms and Agreements */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center space-x-2" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              <Shield className="w-6 h-6 text-blue-600" />
              <span>Terms and Agreements</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="terms"
                  checked={agreements.terms}
                  onCheckedChange={(checked) => handleAgreementChange("terms", checked as boolean)}
                />
                <div className="flex-1">
                  <Label htmlFor="terms" className="text-sm font-medium">
                    I agree to the Terms and Conditions *
                  </Label>
                  <p className="text-xs text-gray-600 mt-1">
                    By checking this box, you agree to our terms of service and conference policies.
                    <a href="#" className="text-blue-600 hover:underline ml-1">Read full terms</a>
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Checkbox
                  id="privacy"
                  checked={agreements.privacy}
                  onCheckedChange={(checked) => handleAgreementChange("privacy", checked as boolean)}
                />
                <div className="flex-1">
                  <Label htmlFor="privacy" className="text-sm font-medium">
                    I agree to the Privacy Policy *
                  </Label>
                  <p className="text-xs text-gray-600 mt-1">
                    We will handle your personal data in accordance with our privacy policy.
                    <a href="#" className="text-blue-600 hover:underline ml-1">Read privacy policy</a>
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Checkbox
                  id="marketing"
                  checked={agreements.marketing}
                  onCheckedChange={(checked) => handleAgreementChange("marketing", checked as boolean)}
                />
                <div className="flex-1">
                  <Label htmlFor="marketing" className="text-sm font-medium">
                    I consent to receive marketing communications
                  </Label>
                  <p className="text-xs text-gray-600 mt-1">
                    Receive updates about future events and opportunities (optional).
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center space-x-2" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              <CreditCard className="w-6 h-6 text-green-600" />
              <span>Payment Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-blue-900 mb-2">Payment Methods Accepted</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• M-Pesa (Kenya)</li>
                <li>• Bank Transfer</li>
                <li>• Credit/Debit Cards (Visa, Mastercard)</li>
                <li>• PayPal</li>
              </ul>
            </div>
            
            <div className="text-center">
              <p className="text-gray-600 mb-4">
                You will be redirected to our secure payment gateway to complete your registration.
              </p>
              
              <Button
                onClick={handleSubmit}
                disabled={!canProceed}
                className="px-12 py-3 text-lg font-semibold"
                style={{ fontFamily: "Myriad Pro, Arial, sans-serif" }}
              >
                Proceed to Payment ({registrationData.price})
              </Button>
              
              {!canProceed && (
                <p className="text-red-600 text-sm mt-2">
                  Please accept the required terms and conditions to proceed.
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
