"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Crown } from "lucide-react"
import {
  saveFormDataToStorage,
  GroupRegistrationState,
  initializeGroupRegistration,
  addGroupMember,
  updateGroupMember,
  removeGroupMember,
  navigateToMember
} from "@/lib/registration-utils"
import { GroupRegistration } from "@/components/registration/GroupRegistration"


const vipCategories = [
  "Government Official",
  "Keynote Speaker",
  "Distinguished Guest",
  "Diplomatic Representative",
  "International Organization Representative",
  "Media Representative",
  "Academic Leader",
  "Industry Leader",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

const specialServices = [
  "Airport pickup/drop-off",
  "Dedicated liaison officer",
  "Translation services",
  "Special dietary requirements",
  "Accessibility accommodations",
  "Security arrangements",
  "Private meeting rooms",
  "Media interview coordination",
]

export default function VIPRegistrationForm() {
  const router = useRouter()
  const [groupState, setGroupState] = useState<GroupRegistrationState>(initializeGroupRegistration())
  const [formData, setFormData] = useState({
    // Personal Information
    title: "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    organization: "",
    position: "",
    country: "",
    city: "",
    
    // VIP Information
    vipCategory: "",
    invitationReference: "",
    biography: "",
    achievements: "",
    
    // Travel Information
    arrivalDate: "",
    departureDate: "",
    accommodationNeeds: "",
    accommodationPreferences: "",
    transportationNeeds: "",
    
    // Special Requirements
    specialServices: [] as string[],
    dietaryRequirements: "",
    accessibilityNeeds: "",
    securityRequirements: "",
    specialRequests: "",
    
    // Media & Protocol
    mediaConsent: false,
    photographyConsent: false,
    protocolRequirements: "",
    
    // Agreements
    termsAccepted: false,
    confidentialityAgreement: false,
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleServiceChange = (service: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      specialServices: checked
        ? [...prev.specialServices, service]
        : prev.specialServices.filter(s => s !== service)
    }))
  }

  // Group registration handlers
  const handleToggleGroupMode = (enabled: boolean) => {
    setGroupState(prev => ({ ...prev, isGroupMode: enabled }))
  }

  const handleAddMember = () => {
    setGroupState(prev => addGroupMember(prev, formData))
    // Reset form for next member
    setFormData({
      title: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      organization: "",
      position: "",
      country: "",
      city: "",
      vipCategory: "",
      invitationReference: "",
      biography: "",
      achievements: "",
      arrivalDate: "",
      departureDate: "",
      accommodationNeeds: "",
      accommodationPreferences: "",
      transportationNeeds: "",
      specialServices: [],
      dietaryRequirements: "",
      accessibilityNeeds: "",
      securityRequirements: "",
      specialRequests: "",
      mediaConsent: false,
      photographyConsent: false,
      protocolRequirements: "",
      termsAccepted: false,
      confidentialityAgreement: false,
    })
  }

  const handleNavigateToMember = (index: number) => {
    // Save current form data before navigating
    setGroupState(prev => updateGroupMember(prev, prev.currentMemberIndex, formData))

    // Navigate to the selected member
    const newState = navigateToMember(groupState, index)
    setGroupState(newState)

    // Load the member's data into the form
    if (index < newState.members.length) {
      const memberData = newState.members[index] as any
      setFormData(memberData)
    } else {
      // New member - reset form
      setFormData({
        title: "",
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        organization: "",
        position: "",
        country: "",
        city: "",
        vipCategory: "",
        invitationReference: "",
        biography: "",
        achievements: "",
        arrivalDate: "",
        departureDate: "",
        accommodationNeeds: "",
        accommodationPreferences: "",
        transportationNeeds: "",
        specialServices: [],
        dietaryRequirements: "",
        accessibilityNeeds: "",
        securityRequirements: "",
        specialRequests: "",
        mediaConsent: false,
        photographyConsent: false,
        protocolRequirements: "",
        termsAccepted: false,
        confidentialityAgreement: false,
      })
    }
  }

  const handleRemoveMember = (index: number) => {
    setGroupState(prev => removeGroupMember(prev, index))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare registration data
    let registrationData

    if (groupState.isGroupMode) {
      // Update current member data and include all group members
      const updatedGroupState = updateGroupMember(groupState, groupState.currentMemberIndex, formData)
      registrationData = {
        registrationType: "VIP Registration",
        isGroupRegistration: true,
        groupMembers: [...updatedGroupState.members, formData],
        amount: `KES ${(updatedGroupState.members.length + 1) * 30000}`,
        pricePerPerson: "KES 30,000",
        submissionDate: new Date().toISOString()
      }
    } else {
      // Single registration
      registrationData = {
        ...formData,
        registrationType: "VIP Registration",
        isGroupRegistration: false,
        amount: "KES 30,000",
        pricePerPerson: "KES 30,000",
        submissionDate: new Date().toISOString()
      }
    }

    saveFormDataToStorage("ikia-registration", registrationData)

    console.log("VIP form submitted:", registrationData)
    router.push("/registration/success")
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>
        
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              VIP Registration
            </h1>
            <p className="text-blue-600 font-semibold">Premium Experience</p>
            <div className="mt-2">
              <span className="text-2xl font-bold text-blue-700">KES 30,000</span>
              <span className="text-sm text-gray-600 ml-2">per person</span>
            </div>
          </div>
        </div>

        {/* VIP Benefits Highlight */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mb-6">
          <h2 className="text-xl font-bold text-blue-900 mb-4" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
            VIP Premium Benefits
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800">Priority seating at all sessions</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800">Exclusive VIP networking lounge access</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800">Premium welcome package</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800">Dedicated concierge service</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800">Private meet & greet with speakers</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800">Complimentary premium refreshments</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800">Fast-track registration & check-in</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800">Exclusive VIP transportation</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Group Registration */}
        <GroupRegistration
          groupState={groupState}
          onToggleGroupMode={handleToggleGroupMode}
          onAddMember={handleAddMember}
          onNavigateToMember={handleNavigateToMember}
          onRemoveMember={handleRemoveMember}
          currentMemberData={formData}
        />

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <Label htmlFor="title">Title</Label>
                <Select value={formData.title} onValueChange={(value) => handleInputChange("title", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select title" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Dr.">Dr.</SelectItem>
                    <SelectItem value="Prof.">Prof.</SelectItem>
                    <SelectItem value="Hon.">Hon.</SelectItem>
                    <SelectItem value="Mr.">Mr.</SelectItem>
                    <SelectItem value="Ms.">Ms.</SelectItem>
                    <SelectItem value="Mrs.">Mrs.</SelectItem>
                    <SelectItem value="H.E.">H.E.</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="organization">Organization/Institution </Label>
                <Input
                  id="organization"
                  value={formData.organization}
                  onChange={(e) => handleInputChange("organization", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="position">Position/Title</Label>
                <Input
                  id="position"
                  value={formData.position}
                  onChange={(e) => handleInputChange("position", e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select value={formData.country} onValueChange={(value) => handleInputChange("country", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="city">City/County *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* VIP Preferences & Services */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold text-blue-700" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              VIP Preferences & Premium Services
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="dietaryRequirements">Dietary Requirements</Label>
              <Input
                id="dietaryRequirements"
                value={formData.dietaryRequirements || ""}
                onChange={(e) => handleInputChange("dietaryRequirements", e.target.value)}
                placeholder="Any special dietary needs or preferences..."
              />
            </div>

            <div>
              <Label htmlFor="accommodationPreferences">Accommodation Preferences</Label>
              <Input
                id="accommodationPreferences"
                value={formData.accommodationPreferences || ""}
                onChange={(e) => handleInputChange("accommodationPreferences", e.target.value)}
                placeholder="Preferred hotel category, location, or specific requirements..."
              />
            </div>

            <div>
              <Label htmlFor="transportationNeeds">Transportation Requirements</Label>
              <Select value={formData.transportationNeeds || ""} onValueChange={(value) => handleInputChange("transportationNeeds", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select transportation preference" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="airport-pickup">Airport Pickup & Drop-off</SelectItem>
                  <SelectItem value="daily-transport">Daily Conference Transport</SelectItem>
                  <SelectItem value="full-chauffeur">Full Chauffeur Service</SelectItem>
                  <SelectItem value="none">No Transportation Required</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="specialRequests">Special Requests</Label>
              <Input
                id="specialRequests"
                value={formData.specialRequests || ""}
                onChange={(e) => handleInputChange("specialRequests", e.target.value)}
                placeholder="Any additional VIP services or special arrangements..."
              />
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 mb-2">VIP Concierge Services</h4>
              <p className="text-sm text-blue-800 mb-3">
                Our dedicated VIP concierge team will contact you within 24 hours to arrange:
              </p>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Personalized itinerary planning</li>
                <li>• Restaurant reservations and recommendations</li>
                <li>• Local attraction bookings</li>
                <li>• Business meeting arrangements</li>
                <li>• Any other special requests</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Continue Button */}
        <div className="flex justify-center">
          <Button
            type="submit"
            className="px-12 py-3 text-lg font-semibold"
            style={{ fontFamily: "Myriad Pro, Arial, sans-serif" }}
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
