"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Building2 } from "lucide-react"
import { SponsorshipTierGrid } from "@/components/registration/PackageGrid"
import { allSponsorshipTiers, countries as countriesList } from "@/lib/registration-data"
import {
  saveFormDataToStorage,
  GroupRegistrationState,
  initializeGroupRegistration,
  addGroupMember,
  updateGroupMember,
  removeGroupMember,
  navigateToMember
} from "@/lib/registration-utils"
import { GroupRegistration } from "@/components/registration/GroupRegistration"

const sponsorshipAreas = [
  "Conference Sessions",
  "Networking Events",
  "Welcome Reception",
  "Gala Dinner",
  "Coffee Breaks",
  "Conference Materials",
  "Technology & Innovation Showcase",
  "Cultural Performances",
  "Site Visits",
  "Awards Ceremony",
]

export default function SponsorRegistrationForm() {
  const router = useRouter()
  const [groupState, setGroupState] = useState<GroupRegistrationState>(initializeGroupRegistration())
  const [formData, setFormData] = useState({
    // Personal Information (for group registration compatibility)
    firstName: "",
    lastName: "",

    // Company Information
    companyName: "",
    contactPerson: "",
    email: "",
    phone: "",
    website: "",
    country: "",
    city: "",
    address: "",
    
    // Company Details
    companyDescription: "",
    industryType: "",
    companySize: "",
    annualRevenue: "",
    
    // Sponsorship Information
    selectedTier: "",
    sponsorshipAreas: [] as string[],
    customRequirements: "",
    marketingObjectives: "",

    // Ticket Allocation
    allocatedTickets: {
      vip: 0,
      delegate: 0,
      total: 0
    },
    ticketDistribution: [] as Array<{
      name: string,
      email: string,
      type: 'VIP' | 'Delegate'
    }>,
    
    // Brand Information
    brandGuidelines: "",
    logoFiles: null as File | null,
    marketingMaterials: "",
    
    // Representatives
    representative1Name: "",
    representative1Email: "",
    representative1Position: "",
    representative2Name: "",
    representative2Email: "",
    representative2Position: "",
    
    // Special Package Details
    mediaPartnerDetails: "",
    receptionRequirements: "",
    merchandiseSpecs: "",
    technologySpecs: "",

    // Agreements
    termsAccepted: false,
    sponsorshipAgreement: false,
    marketingConsent: false,
  })

  // Calculate ticket allocation based on sponsorship tier
  const calculateTicketAllocation = (tierName: string) => {
    // Find the tier in the data to get exact ticket allocation
    const tier = allSponsorshipTiers.find(t => t.name === tierName)
    if (tier) {
      return {
        vip: tier.vipTickets || 0,
        delegate: tier.delegateTickets || 0,
        total: (tier.vipTickets || 0) + (tier.delegateTickets || 0)
      }
    }

    // Fallback for manual allocations if tier not found
    const allocations = {
      "Title Sponsor": { vip: 15, delegate: 0, total: 15 },
      "Platinum Sponsor": { vip: 5, delegate: 5, total: 10 },
      "Gold Sponsor": { vip: 2, delegate: 3, total: 5 },
      "Silver Sponsor": { vip: 1, delegate: 2, total: 3 },
      "Bronze Sponsor": { vip: 0, delegate: 2, total: 2 },
    }
    return allocations[tierName as keyof typeof allocations] || { vip: 0, delegate: 0, total: 0 }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      }

      // Update ticket allocation when tier changes
      if (field === 'selectedTier' && value) {
        const allocation = calculateTicketAllocation(value)
        newData.allocatedTickets = allocation
        // Reset ticket distribution when tier changes
        newData.ticketDistribution = []
      }

      return newData
    })
  }

  // Ticket distribution management
  const addTicketRecipient = () => {
    if (formData.ticketDistribution.length < formData.allocatedTickets.total) {
      setFormData(prev => ({
        ...prev,
        ticketDistribution: [
          ...prev.ticketDistribution,
          { name: "", email: "", type: "Delegate" as const }
        ]
      }))
    }
  }

  const updateTicketRecipient = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      ticketDistribution: prev.ticketDistribution.map((ticket, i) =>
        i === index ? { ...ticket, [field]: value } : ticket
      )
    }))
  }

  const removeTicketRecipient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      ticketDistribution: prev.ticketDistribution.filter((_, i) => i !== index)
    }))
  }

  const getAvailableTicketTypes = (currentIndex: number) => {
    const usedVipTickets = formData.ticketDistribution.filter((ticket, index) =>
      index !== currentIndex && ticket.type === 'VIP'
    ).length
    const usedDelegateTickets = formData.ticketDistribution.filter((ticket, index) =>
      index !== currentIndex && ticket.type === 'Delegate'
    ).length

    const availableTypes = []
    if (usedVipTickets < formData.allocatedTickets.vip) {
      availableTypes.push('VIP')
    }
    if (usedDelegateTickets < formData.allocatedTickets.delegate) {
      availableTypes.push('Delegate')
    }

    return availableTypes
  }

  const handleSponsorshipAreaChange = (area: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      sponsorshipAreas: checked 
        ? [...prev.sponsorshipAreas, area]
        : prev.sponsorshipAreas.filter(a => a !== area)
    }))
  }

  // Group registration handlers
  const handleToggleGroupMode = (enabled: boolean) => {
    setGroupState(prev => ({ ...prev, isGroupMode: enabled }))
  }

  const handleAddMember = () => {
    setGroupState(prev => addGroupMember(prev, formData))
    // Reset form for next member
    setFormData({
      // Personal Information (for group registration compatibility)
      firstName: "",
      lastName: "",

      // Company Information
      companyName: "",
      contactPerson: "",
      email: "",
      phone: "",
      website: "",
      country: "",
      city: "",
      address: "",

      // Company Details
      companyDescription: "",
      industryType: "",
      companySize: "",
      annualRevenue: "",

      // Sponsorship Information
      selectedTier: "",
      sponsorshipAreas: [] as string[],
      customRequirements: "",
      marketingObjectives: "",

      // Ticket Allocation
      allocatedTickets: {
        vip: 0,
        delegate: 0,
        total: 0
      },
      ticketDistribution: [] as Array<{
        name: string,
        email: string,
        type: 'VIP' | 'Delegate'
      }>,

      // Brand Information
      brandGuidelines: "",
      logoFiles: null as File | null,
      marketingMaterials: "",

      // Representatives
      representative1Name: "",
      representative1Email: "",
      representative1Position: "",
      representative2Name: "",
      representative2Email: "",
      representative2Position: "",

      // Special Package Details
      mediaPartnerDetails: "",
      receptionRequirements: "",
      merchandiseSpecs: "",
      technologySpecs: "",

      // Agreements
      termsAccepted: false,
      sponsorshipAgreement: false,
      marketingConsent: false,
    })
  }

  const handleNavigateToMember = (index: number) => {
    // Save current form data before navigating
    setGroupState(prev => updateGroupMember(prev, prev.currentMemberIndex, formData))

    // Navigate to the selected member
    const newState = navigateToMember(groupState, index)
    setGroupState(newState)

    // Load the member's data into the form
    if (index < newState.members.length) {
      const memberData = newState.members[index] as any
      setFormData(memberData)
    } else {
      // New member - reset form
      setFormData({
        // Personal Information (for group registration compatibility)
        firstName: "",
        lastName: "",

        // Company Information
        companyName: "",
        contactPerson: "",
        email: "",
        phone: "",
        website: "",
        country: "",
        city: "",
        address: "",

        // Company Details
        companyDescription: "",
        industryType: "",
        companySize: "",
        annualRevenue: "",

        // Sponsorship Information
        selectedTier: "",
        sponsorshipAreas: [] as string[],
        customRequirements: "",
        marketingObjectives: "",

        // Ticket Allocation
        allocatedTickets: {
          vip: 0,
          delegate: 0,
          total: 0
        },
        ticketDistribution: [] as Array<{
          name: string,
          email: string,
          type: 'VIP' | 'Delegate'
        }>,

        // Brand Information
        brandGuidelines: "",
        logoFiles: null as File | null,
        marketingMaterials: "",

        // Representatives
        representative1Name: "",
        representative1Email: "",
        representative1Position: "",
        representative2Name: "",
        representative2Email: "",
        representative2Position: "",

        // Special Package Details
        mediaPartnerDetails: "",
        receptionRequirements: "",
        merchandiseSpecs: "",
        technologySpecs: "",

        // Agreements
        termsAccepted: false,
        sponsorshipAgreement: false,
        marketingConsent: false,
      })
    }
  }

  const handleRemoveMember = (index: number) => {
    setGroupState(prev => removeGroupMember(prev, index))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare registration data
    let registrationData

    if (groupState.isGroupMode) {
      // Update current member data and include all group members
      const updatedGroupState = updateGroupMember(groupState, groupState.currentMemberIndex, formData)
      registrationData = {
        registrationType: "Conference Sponsor",
        isGroupRegistration: true,
        groupMembers: [...updatedGroupState.members, formData],
        submissionDate: new Date().toISOString()
      }
    } else {
      // Single registration
      registrationData = {
        ...formData,
        registrationType: "Conference Sponsor",
        isGroupRegistration: false,
        submissionDate: new Date().toISOString()
      }
    }

    saveFormDataToStorage("ikia-registration", registrationData)

    console.log("Sponsor form submitted:", registrationData)
    router.push("/registration/success")
  }



  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>
        
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-orange-700 to-red-700 rounded-2xl flex items-center justify-center">
            <Building2 className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Conference Sponsor Registration
            </h1>
            <p className="text-orange-600 font-semibold">Partner for Impact</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Group Registration */}
        <GroupRegistration
          groupState={groupState}
          onToggleGroupMode={handleToggleGroupMode}
          onAddMember={handleAddMember}
          onNavigateToMember={handleNavigateToMember}
          onRemoveMember={handleRemoveMember}
          currentMemberData={formData}
        />

        {/* Sponsorship Tier Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Select Sponsorship Tier
            </CardTitle>
          </CardHeader>
          <CardContent>
            <SponsorshipTierGrid
              tiers={allSponsorshipTiers}
              selectedTier={formData.selectedTier}
              onTierSelect={(tierId) => handleInputChange("selectedTier", tierId)}
            />
          </CardContent>
        </Card>

        {/* Ticket Allocation System */}
        {formData.selectedTier && formData.allocatedTickets.total > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-bold text-green-700" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
                Ticket Allocation & Distribution
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Allocation Summary */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-semibold text-green-900 mb-3">Your Sponsorship Benefits</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-700">{formData.allocatedTickets.vip}</div>
                    <div className="text-sm text-green-600">VIP Tickets</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-700">{formData.allocatedTickets.delegate}</div>
                    <div className="text-sm text-green-600">Delegate Tickets</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-700">{formData.allocatedTickets.total}</div>
                    <div className="text-sm text-green-600">Total Tickets</div>
                  </div>
                </div>
              </div>

              {/* Ticket Distribution */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <Label className="text-base font-semibold">
                    Ticket Recipients ({formData.ticketDistribution.length}/{formData.allocatedTickets.total})
                  </Label>
                  {formData.ticketDistribution.length < formData.allocatedTickets.total && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addTicketRecipient}
                      className="text-green-600 border-green-300 hover:bg-green-50"
                    >
                      Add Recipient
                    </Button>
                  )}
                </div>

                {formData.ticketDistribution.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No ticket recipients added yet.</p>
                    <p className="text-sm">Click "Add Recipient" to start distributing your allocated tickets.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {formData.ticketDistribution.map((ticket, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <Label htmlFor={`recipient-name-${index}`}>Full Name *</Label>
                            <Input
                              id={`recipient-name-${index}`}
                              value={ticket.name}
                              onChange={(e) => updateTicketRecipient(index, 'name', e.target.value)}
                              placeholder="Enter full name"
                              required
                            />
                          </div>
                          <div>
                            <Label htmlFor={`recipient-email-${index}`}>Email Address *</Label>
                            <Input
                              id={`recipient-email-${index}`}
                              type="email"
                              value={ticket.email}
                              onChange={(e) => updateTicketRecipient(index, 'email', e.target.value)}
                              placeholder="Enter email address"
                              required
                            />
                          </div>
                          <div>
                            <Label htmlFor={`recipient-type-${index}`}>Ticket Type *</Label>
                            <div className="flex items-center space-x-2">
                              <Select
                                value={ticket.type}
                                onValueChange={(value) => updateTicketRecipient(index, 'type', value)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  {getAvailableTicketTypes(index).map((type) => (
                                    <SelectItem key={type} value={type}>
                                      {type}
                                    </SelectItem>
                                  ))}
                                  {/* Always show current selection even if no longer available */}
                                  {!getAvailableTicketTypes(index).includes(ticket.type) && (
                                    <SelectItem value={ticket.type}>
                                      {ticket.type}
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removeTicketRecipient(index)}
                                className="text-red-600 border-red-300 hover:bg-red-50"
                              >
                                Remove
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {formData.ticketDistribution.length > 0 && formData.ticketDistribution.length < formData.allocatedTickets.total && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm text-yellow-800">
                    <strong>Note:</strong> You have {formData.allocatedTickets.total - formData.ticketDistribution.length} unallocated tickets.
                    You can add more recipients or allocate them later.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Special Sponsorship Package Details */}
        {formData.selectedTier && (() => {
          const selectedTier = allSponsorshipTiers.find(tier => tier.name === formData.selectedTier)
          return selectedTier?.category === 'special' ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold text-purple-700" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
                  Special Sponsorship Package Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h4 className="font-semibold text-purple-900 mb-2">{selectedTier.name}</h4>
                  <p className="text-purple-800 mb-3">{selectedTier.description}</p>
                  <div className="text-lg font-bold text-purple-700 mb-3">
                    Investment: {selectedTier.price}
                  </div>

                  <div>
                    <h5 className="font-semibold text-purple-900 mb-2">Exclusive Benefits:</h5>
                    <ul className="space-y-1">
                      {selectedTier.benefits.map((benefit, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                          <span className="text-sm text-purple-800">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Special Package Requirements */}
                {selectedTier.id === 'media-partner' && (
                  <div>
                    <Label htmlFor="mediaPartnerDetails">Media Partnership Details</Label>
                    <Textarea
                      id="mediaPartnerDetails"
                      value={formData.mediaPartnerDetails || ""}
                      onChange={(e) => handleInputChange("mediaPartnerDetails", e.target.value)}
                      placeholder="Please describe your media platform, audience reach, and proposed coverage plan..."
                      rows={4}
                    />
                  </div>
                )}

                {selectedTier.id === 'networking-reception' && (
                  <div>
                    <Label htmlFor="receptionRequirements">Reception Sponsorship Requirements</Label>
                    <Textarea
                      id="receptionRequirements"
                      value={formData.receptionRequirements || ""}
                      onChange={(e) => handleInputChange("receptionRequirements", e.target.value)}
                      placeholder="Any specific requirements for the networking reception (catering preferences, branding specifications, etc.)..."
                      rows={4}
                    />
                  </div>
                )}

                {selectedTier.id === 'merchandise' && (
                  <div>
                    <Label htmlFor="merchandiseSpecs">Merchandise Specifications</Label>
                    <Textarea
                      id="merchandiseSpecs"
                      value={formData.merchandiseSpecs || ""}
                      onChange={(e) => handleInputChange("merchandiseSpecs", e.target.value)}
                      placeholder="Describe your proposed merchandise items, branding requirements, and distribution preferences..."
                      rows={4}
                    />
                  </div>
                )}

                {selectedTier.id === 'internet' && (
                  <div>
                    <Label htmlFor="technologySpecs">Technology Service Specifications</Label>
                    <Textarea
                      id="technologySpecs"
                      value={formData.technologySpecs || ""}
                      onChange={(e) => handleInputChange("technologySpecs", e.target.value)}
                      placeholder="Detail your technology services, internet capacity, equipment specifications, and technical support plan..."
                      rows={4}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          ) : null
        })()}

        {/* Company Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Company Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="companyName">Company/Organization Name *</Label>
              <Input
                id="companyName"
                value={formData.companyName}
                onChange={(e) => handleInputChange("companyName", e.target.value)}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="contactPerson">Primary Contact Person *</Label>
                <Input
                  id="contactPerson"
                  value={formData.contactPerson}
                  onChange={(e) => handleInputChange("contactPerson", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="https://www.example.com"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select value={formData.country} onValueChange={(value) => handleInputChange("country", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countriesList.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="companyDescription">Company Description *</Label>
              <Textarea
                id="companyDescription"
                placeholder="Provide a detailed description of your company and its mission..."
                value={formData.companyDescription}
                onChange={(e) => handleInputChange("companyDescription", e.target.value)}
                required
                rows={4}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="industryType">Industry Type *</Label>
                <Input
                  id="industryType"
                  value={formData.industryType}
                  onChange={(e) => handleInputChange("industryType", e.target.value)}
                  required
                  placeholder="e.g., Technology, Healthcare, Finance"
                />
              </div>
              <div>
                <Label htmlFor="companySize">Company Size</Label>
                <Select value={formData.companySize} onValueChange={(value) => handleInputChange("companySize", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select company size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-10">1-10 employees</SelectItem>
                    <SelectItem value="11-50">11-50 employees</SelectItem>
                    <SelectItem value="51-200">51-200 employees</SelectItem>
                    <SelectItem value="201-1000">201-1000 employees</SelectItem>
                    <SelectItem value="1000+">1000+ employees</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sponsorship Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Sponsorship Preferences
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="text-base font-semibold mb-4 block">
                Specific Sponsorship Areas of Interest (Select all that apply)
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {sponsorshipAreas.map((area) => (
                  <div key={area} className="flex items-center space-x-2">
                    <Checkbox
                      id={area}
                      checked={formData.sponsorshipAreas.includes(area)}
                      onCheckedChange={(checked) => handleSponsorshipAreaChange(area, checked as boolean)}
                    />
                    <Label htmlFor={area} className="text-sm">
                      {area}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="marketingObjectives">Marketing Objectives</Label>
              <Textarea
                id="marketingObjectives"
                placeholder="What are your key marketing objectives for this sponsorship?"
                value={formData.marketingObjectives}
                onChange={(e) => handleInputChange("marketingObjectives", e.target.value)}
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="customRequirements">Custom Requirements</Label>
              <Textarea
                id="customRequirements"
                placeholder="Any specific requirements or custom sponsorship ideas..."
                value={formData.customRequirements}
                onChange={(e) => handleInputChange("customRequirements", e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Continue Button */}
        <div className="flex justify-center">
          <Button
            type="submit"
            className="px-12 py-3 text-lg font-semibold"
            style={{ fontFamily: "Myriad Pro, Arial, sans-serif" }}
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
