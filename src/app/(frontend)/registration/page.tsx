"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import {
  Users,
  Store,
  TrendingUp,
  Crown,
  Building2,
  Check,
  Sparkles,
  Calendar,
  MapPin,
  Globe,
  Target,
  Award,
  Handshake,
} from "lucide-react"

const userTypes = [
  {
    id: "guest",
    title: "Delegate",
    subtitle: "Join the Knowledge Exchange",
    description:
      "Participate in sessions, workshops, and networking to explore Indigenous Knowledge innovations and investment opportunities.",
    icon: Users,
    color: "green",
    gradient: "from-ikia-green-500 to-ikia-green-600",
    features: [
      "Access to all conference sessions and workshops",
      "Site visits and exhibition area access",
      "Gift pack with branded items and cultural souvenir",
      "Conference materials and program booklet",
      "Tea breaks and lunch for all days",
      "Certificate of participation",
    ],
    popular: true,
    badge: "Most Popular",
  },
  {
    id: "exhibitor",
    title: "Exhibitor",
    subtitle: "Showcase Your Innovation",
    description:
      "Display your Indigenous Knowledge-based products and services to investors, buyers, and international partners.",
    icon: Store,
    color: "brown",
    gradient: "from-ikia-brown-500 to-ikia-brown-600",
    features: [
      "Standard 3x3m booth with setup support",
      "Delegate passes for 2 people for 3 days",
      "Access to dealrooms and networking sessions",
      "Media coverage and promotional opportunities",
      "Gift hamper and dinner inclusion",
      "Certificate of participation",
    ],
    popular: false,
    badge: "Best Value",
  },
  {
    id: "investor",
    title: "Investment Discovery",
    subtitle: "Explore Opportunities",
    description:
      "Discover Indigenous Knowledge investment opportunities and connect with innovative enterprises as a delegate with investment interests.",
    icon: TrendingUp,
    color: "ochre",
    gradient: "from-ikia-yellow-500 to-ikia-yellow-600",
    features: [
      "Access to investment showcase sessions",
      "Networking with IKIA holders and entrepreneurs",
      "Market intelligence and due diligence support",
      "Investment opportunity presentations",
      "Post-conference follow-up and connections",
      "All standard delegate benefits included",
    ],
    popular: false,
    badge: "Investment Focus",
  },
  {
    id: "vip",
    title: "VIP",
    subtitle: "Premium Experience",
    description:
      "Exclusive access for dignitaries, keynote speakers, government officials, and distinguished guests with premium amenities.",
    icon: Crown,
    color: "blue",
    gradient: "from-ikia-blue-500 to-ikia-blue-600",
    features: [
      "Access to VIP dealrooms and holding areas",
      "Gala dinner and exclusive networking events",
      "Priority registration desk and VIP car badge",
      "Premium gift pack with flash drive",
      "All delegate benefits plus VIP amenities",
      "Downloadable VIP car sticker upon payment",
    ],
    popular: false,
    badge: "Premium",
  },
  {
    id: "sponsor",
    title: "Conference Sponsor",
    subtitle: "Partner for Impact",
    description:
      "Support Indigenous Knowledge innovation while gaining brand visibility, thought leadership, and strategic networking opportunities.",
    icon: Building2,
    color: "sienna",
    gradient: "from-ikia-sienna-500 to-ikia-sienna-600",
    features: [
      "Brand visibility across all conference materials",
      "Speaking opportunities and thought leadership",
      "Premium networking privileges and access",
      "Custom partnership packages available",
      "Year-round association with IKIA initiative",
      "Multiple sponsorship tiers available",
    ],
    popular: false,
    badge: "Partnership",
  },
]

const conferenceStats = [
  { icon: Globe, label: "Countries Represented", value: "13+" },
  { icon: Target, label: "IKIA Projects", value: "100+" },
  { icon: Award, label: "Innovation Categories", value: "6" },
  { icon: Handshake, label: "Expected Attendees", value: "500+" },
]

export default function UserTypeSelection() {
  const [selectedType, setSelectedType] = useState<string>("")
  const [hoveredType, setHoveredType] = useState<string>("")
  const router = useRouter()



  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Minimalistic Hero Section */}
      <div className="text-center mb-16 relative">
        <div className="relative bg-gradient-to-br from-background via-card to-muted/20 rounded-2xl p-8 md:p-12 border border-border overflow-hidden">
          {/* Subtle Heritage Elements */}
          <div className="absolute top-6 left-8 w-3 h-3 bg-secondary/30 rounded-full animate-pulse"></div>
          <div className="absolute top-12 right-12 w-2 h-2 bg-accent/40 rounded-full animate-pulse" style={{ animationDelay: "1s" }}></div>
          <div className="absolute bottom-8 left-12 w-4 h-4 bg-primary/20 rounded-full animate-pulse" style={{ animationDelay: "2s" }}></div>

          <div className="relative z-10">
            {/* Simple Logo */}
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-xl mb-6 shadow-lg">
              <Sparkles className="w-8 h-8 text-primary-foreground" />
            </div>

            <h1 className="text-3xl md:text-5xl font-bold text-foreground mb-4 font-acquire">
              Choose Your <span className="text-primary">Registration Type</span>
            </h1>

            <div className="max-w-3xl mx-auto mb-6">
              <p className="text-base md:text-lg text-muted-foreground mb-4 font-myriad">
                Join the <span className="text-secondary font-semibold">1st International Investment Conference</span> on{" "}
                <span className="text-accent font-semibold">Indigenous Knowledge Intellectual Assets</span>
              </p>

              <div className="flex flex-wrap justify-center items-center gap-4 text-sm text-muted-foreground mb-4">
                <div className="flex items-center space-x-2 bg-card px-3 py-1.5 rounded-lg border border-border">
                  <Calendar className="w-4 h-4 text-secondary" />
                  <span className="font-medium">November 18-21, 2025</span>
                </div>
                <div className="flex items-center space-x-2 bg-card px-3 py-1.5 rounded-lg border border-border">
                  <MapPin className="w-4 h-4 text-accent" />
                  <span className="font-medium">Thika Greens Golf Resort</span>
                </div>
              </div>

              <p className="text-muted-foreground text-sm italic">
                &ldquo;Where <span className="text-secondary">ancestral wisdom</span> meets{" "}
                <span className="text-primary">future innovation</span>&rdquo;
              </p>
            </div>

            {/* Simplified Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-2xl mx-auto">
              {conferenceStats.map((stat, index) => {
                const IconComponent = stat.icon
                return (
                  <div key={index} className="text-center bg-card/50 backdrop-blur-sm rounded-lg p-3 border border-border">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center mx-auto mb-2">
                      <IconComponent className="w-4 h-4 text-primary-foreground" />
                    </div>
                    <p className="text-lg font-bold text-foreground mb-1">{stat.value}</p>
                    <p className="text-xs text-muted-foreground">{stat.label}</p>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Registration Types Grid */}
      <div className="space-y-6 mb-16">
        {/* First row - 3 cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {userTypes.slice(0, 3).map((type) => {
            const IconComponent = type.icon
            const isSelected = selectedType === type.id
            const isHovered = hoveredType === type.id

            return (
              <Card
                key={type.id}
                className={`
                  cursor-pointer transition-all duration-500 overflow-hidden group relative
                  ${
                    isSelected
                      ? "ring-4 ring-secondary shadow-2xl transform -translate-y-4 bg-gradient-to-br from-card to-secondary/5"
                      : "hover:shadow-2xl hover:-translate-y-2 bg-card"
                  }
                  ${type.popular ? "border-2 border-accent" : "border border-border"}
                `}
                onClick={() => {
                  setSelectedType(type.id)
                  router.push(`/registration/form/${type.id}`)
                }}
                onMouseEnter={() => setHoveredType(type.id)}
                onMouseLeave={() => setHoveredType("")}
              >
                {/* Badge */}
                <div className="absolute top-4 right-4 z-20">
                  <div
                    className={`
                    px-3 py-1 rounded-full text-xs font-semibold
                    ${
                      type.popular
                        ? "bg-gradient-to-r from-accent to-destructive text-white"
                        : "bg-muted text-muted-foreground"
                    }
                  `}
                  >
                    {type.badge}
                  </div>
                </div>

                <CardContent className="p-6 relative z-10">
                  {/* Icon and Title Section */}
                  <div className="mb-6">
                    <div className="relative inline-block mb-4">
                      <div
                        className={`
                        w-16 h-16 rounded-xl flex items-center justify-center shadow-lg transition-all duration-500
                        bg-gradient-to-br ${type.gradient}
                        ${isSelected || isHovered ? "scale-110 rotate-3" : ""}
                      `}
                      >
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      {isSelected && (
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-secondary rounded-full flex items-center justify-center shadow-lg">
                          <Check className="w-4 h-4 text-white" />
                        </div>
                      )}
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-accent rounded-full animate-pulse"></div>
                    </div>

                    <h3 className="text-lg font-bold text-foreground mb-2 font-acquire">{type.title}</h3>
                    <p className="text-secondary font-semibold mb-3 text-sm">{type.subtitle}</p>
                    <p className="text-muted-foreground leading-relaxed text-sm">{type.description}</p>
                  </div>

                  {/* Features List */}
                  <div className="space-y-2 mb-4">
                    {type.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start space-x-2">
                        <div className="w-4 h-4 rounded-full bg-success/20 dark:bg-success/30 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check className="w-2.5 h-2.5 text-success" />
                        </div>
                        <span className="text-xs text-muted-foreground leading-relaxed">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Selection Indicator */}
                  {isSelected && (
                    <div className="flex items-center justify-center space-x-2 text-success bg-success/10 dark:bg-success/20 px-3 py-2 rounded-lg">
                      <Check className="w-4 h-4" />
                      <span className="text-sm font-semibold">Selected</span>
                    </div>
                  )}
                </CardContent>

                {/* Selection Indicator Bar */}
                <div
                  className={`
                  absolute bottom-0 left-0 right-0 h-1 transition-all duration-500
                  ${
                    isSelected
                      ? `bg-gradient-to-r ${type.gradient} scale-x-100`
                      : "bg-gradient-to-r from-border to-muted scale-x-0 group-hover:scale-x-100"
                  }
                `}
                ></div>
              </Card>
            )
          })}
        </div>

        {/* Second row - 2 cards centered */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          {userTypes.slice(3).map((type) => {
            const IconComponent = type.icon
            const isSelected = selectedType === type.id
            const isHovered = hoveredType === type.id

            return (
              <Card
                key={type.id}
                className={`
                  cursor-pointer transition-all duration-500 overflow-hidden group relative
                  ${
                    isSelected
                      ? "ring-4 ring-secondary shadow-2xl transform -translate-y-4 bg-gradient-to-br from-card to-secondary/5"
                      : "hover:shadow-2xl hover:-translate-y-2 bg-card"
                  }
                  ${type.popular ? "border-2 border-accent" : "border border-border"}
                `}
                onClick={() => {
                  setSelectedType(type.id)
                  router.push(`/registration/form/${type.id}`)
                }}
                onMouseEnter={() => setHoveredType(type.id)}
                onMouseLeave={() => setHoveredType("")}
              >
                {/* Badge */}
                <div className="absolute top-4 right-4 z-20">
                  <div
                    className={`
                    px-3 py-1 rounded-full text-xs font-semibold
                    ${
                      type.popular
                        ? "bg-gradient-to-r from-accent to-destructive text-white"
                        : "bg-muted text-muted-foreground"
                    }
                  `}
                  >
                    {type.badge}
                  </div>
                </div>

                <CardContent className="p-6 relative z-10">
                  {/* Icon and Title Section */}
                  <div className="mb-6">
                    <div className="relative inline-block mb-4">
                      <div
                        className={`
                        w-16 h-16 rounded-xl flex items-center justify-center shadow-lg transition-all duration-500
                        bg-gradient-to-br ${type.gradient}
                        ${isSelected || isHovered ? "scale-110 rotate-3" : ""}
                      `}
                      >
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      {isSelected && (
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-secondary rounded-full flex items-center justify-center shadow-lg">
                          <Check className="w-4 h-4 text-white" />
                        </div>
                      )}
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-accent rounded-full animate-pulse"></div>
                    </div>

                    <h3 className="text-lg font-bold text-foreground mb-2 font-acquire">{type.title}</h3>
                    <p className="text-secondary font-semibold mb-3 text-sm">{type.subtitle}</p>
                    <p className="text-muted-foreground leading-relaxed text-sm">{type.description}</p>
                  </div>

                  {/* Features List */}
                  <div className="space-y-2 mb-4">
                    {type.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start space-x-2">
                        <div className="w-4 h-4 rounded-full bg-success/20 dark:bg-success/30 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check className="w-2.5 h-2.5 text-success" />
                        </div>
                        <span className="text-xs text-muted-foreground leading-relaxed">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Selection Indicator */}
                  {isSelected && (
                    <div className="flex items-center justify-center space-x-2 text-success bg-success/10 dark:bg-success/20 px-3 py-2 rounded-lg">
                      <Check className="w-4 h-4" />
                      <span className="text-sm font-semibold">Selected</span>
                    </div>
                  )}
                </CardContent>

                {/* Selection Indicator Bar */}
                <div
                  className={`
                  absolute bottom-0 left-0 right-0 h-1 transition-all duration-500
                  ${
                    isSelected
                      ? `bg-gradient-to-r ${type.gradient} scale-x-100`
                      : "bg-gradient-to-r from-border to-muted scale-x-0 group-hover:scale-x-100"
                  }
                `}
                ></div>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Selection Guidance */}
      <div className="text-center relative">
        <div className="mt-6 animate-fade-in-up">
          <p className="text-muted-foreground mb-3 text-sm">Click on any registration type above to begin your registration</p>
          <div className="flex justify-center items-center space-x-3">
            <div className="w-1.5 h-1.5 bg-secondary rounded-full animate-pulse"></div>
            <div className="w-6 h-0.5 bg-gradient-to-r from-secondary to-accent"></div>
            <div
              className="w-1.5 h-1.5 bg-accent rounded-full animate-pulse"
              style={{ animationDelay: "0.5s" }}
            ></div>
            <div className="w-6 h-0.5 bg-gradient-to-r from-accent to-primary"></div>
            <div className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse" style={{ animationDelay: "1s" }}></div>
          </div>
        </div>
      </div>
    </div>
  )
}
