import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, Calendar, MapPin, Users, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface EventPageProps {
  params: Promise<{ slug: string }>
}

const getEventData = (slug: string) => {
  const events: Record<string, any> = {
    'global-business-summit-2024': {
      title: 'Global Business Summit 2024',
      subtitle: 'Join industry leaders for insights on the future of business and innovation',
      organizer: 'Business Council',
      organizerImage: '/placeholder.svg?height=50&width=50',
      eventDate: '2024-03-15',
      endDate: '2024-03-17',
      location: 'Convention Center, New York',
      capacity: '1000 attendees',
      category: 'Summit',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'The Global Business Summit 2024 brings together industry leaders, entrepreneurs, and innovators from around the world to discuss the future of business and share insights on emerging trends.',
        },
        {
          type: 'paragraph',
          text: 'This three-day event features keynote presentations, panel discussions, networking sessions, and workshops covering topics such as digital transformation, sustainable business practices, and global market opportunities.',
        },
      ],
    },
    'tech-innovation-workshop': {
      title: 'Tech Innovation Workshop',
      subtitle:
        'Hands-on workshop exploring the latest technological innovations and their business applications',
      organizer: 'Tech Institute',
      organizerImage: '/placeholder.svg?height=50&width=50',
      eventDate: '2024-02-20',
      endDate: '2024-02-20',
      location: 'Innovation Hub, San Francisco',
      capacity: '150 participants',
      category: 'Workshop',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'This intensive workshop provides hands-on experience with cutting-edge technologies including artificial intelligence, blockchain, and IoT solutions.',
        },
      ],
    },
    'leadership-conference-2024': {
      title: 'Leadership Excellence Conference 2024',
      subtitle: "Developing next-generation leaders for tomorrow's challenges",
      organizer: 'Leadership Institute',
      organizerImage: '/placeholder.svg?height=50&width=50',
      eventDate: '2024-04-10',
      endDate: '2024-04-12',
      location: 'Grand Hotel, Chicago',
      capacity: '500 leaders',
      category: 'Conference',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: "The Leadership Excellence Conference focuses on developing the skills and mindset needed to lead effectively in today's rapidly changing business environment.",
        },
      ],
    },
    'startup-pitch-competition': {
      title: 'Startup Pitch Competition',
      subtitle: 'Emerging startups compete for funding and mentorship opportunities',
      organizer: 'Startup Accelerator',
      organizerImage: '/placeholder.svg?height=50&width=50',
      eventDate: '2024-05-08',
      endDate: '2024-05-08',
      location: 'Innovation Center, Austin',
      capacity: '300 attendees',
      category: 'Competition',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Watch promising startups pitch their innovative ideas to a panel of investors and industry experts in this exciting competition format.',
        },
      ],
    },
    'digital-marketing-summit': {
      title: 'Digital Marketing Summit',
      subtitle: 'Latest trends and strategies in digital marketing and customer engagement',
      organizer: 'Marketing Association',
      organizerImage: '/placeholder.svg?height=50&width=50',
      eventDate: '2024-06-15',
      endDate: '2024-06-16',
      location: 'Conference Center, Los Angeles',
      capacity: '800 marketers',
      category: 'Summit',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Explore the latest digital marketing trends, tools, and strategies that are driving customer engagement and business growth.',
        },
      ],
    },
    'sustainability-forum': {
      title: 'Corporate Sustainability Forum',
      subtitle: 'Building sustainable business practices for long-term success',
      organizer: 'Sustainability Council',
      organizerImage: '/placeholder.svg?height=50&width=50',
      eventDate: '2024-07-20',
      endDate: '2024-07-21',
      location: 'Green Center, Seattle',
      capacity: '400 executives',
      category: 'Forum',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Learn how to integrate sustainability into your business strategy while maintaining profitability and competitive advantage.',
        },
      ],
    },
  }

  return events[slug] || null
}

export default async function EventPage({ params }: EventPageProps) {
  const { slug } = await params
  const event = getEventData(slug)

  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Event Not Found</h1>
          <Link href="/news-media" className="text-blue-600 hover:underline">
            Back to News & Media
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Link
          href="/news-media"
          className="inline-flex items-center text-red-600 hover:text-red-700 mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to News & Media
        </Link>

        <div className="mb-8">
          <Badge className="bg-green-600 text-white mb-4">{event.category}</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {event.title}
          </h1>
          <p className="text-xl text-gray-600 mb-6 leading-relaxed">{event.subtitle}</p>

          <div className="flex items-center space-x-4 border-b border-gray-200 pb-6">
            <Image
              src={event.organizerImage || '/placeholder.svg'}
              alt={event.organizer}
              width={50}
              height={50}
              className="rounded-full"
            />
            <div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="font-medium text-gray-900">{event.organizer}</span>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(event.eventDate).toLocaleDateString()}
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-1" />
                  {event.location}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="relative h-96 md:h-[500px] mb-8 rounded-lg overflow-hidden">
          <Image
            src={event.featuredImage || '/placeholder.svg'}
            alt={event.title}
            fill
            className="object-cover"
          />
        </div>

        {/* Event Details Card */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">Event Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-blue-500" />
                <span className="font-medium">Date:</span>
                <span>
                  {new Date(event.eventDate).toLocaleDateString()} -{' '}
                  {new Date(event.endDate).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-5 h-5 text-red-500" />
                <span className="font-medium">Location:</span>
                <span>{event.location}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-green-500" />
                <span className="font-medium">Capacity:</span>
                <span>{event.capacity}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="prose prose-lg max-w-none">
          {event.content.map((block: any, index: number) => (
            <p key={index} className="text-gray-700 leading-relaxed mb-6 text-lg">
              {block.text}
            </p>
          ))}
        </div>

        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Image
                src={event.organizerImage || '/placeholder.svg'}
                alt={event.organizer}
                width={60}
                height={60}
                className="rounded-full"
              />
              <div>
                <h3 className="font-semibold text-gray-900">{event.organizer}</h3>
                <p className="text-gray-600">Event Organizer</p>
              </div>
            </div>
            <Button asChild>
              <Link href="/news-media">View More Events</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
