import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, Calendar, Heart, MessageCircle, Share } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface SocialPageProps {
  params: Promise<{ slug: string }>
}

const getSocialData = (slug: string) => {
  const socialPosts: Record<string, any> = {
    'behind-the-scenes': {
      title: 'Behind the Scenes: Our Company Culture',
      subtitle: 'Take a look at what makes our workplace special and how we foster innovation',
      author: 'Social Media Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-18',
      platform: 'LinkedIn',
      category: 'Culture',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      engagement: { likes: 1250, comments: 45, shares: 78 },
      content: [
        {
          type: 'paragraph',
          text: "Our company culture is built on collaboration, innovation, and mutual respect. Here's a glimpse into what makes our workplace truly special.",
        },
      ],
    },
    'entrepreneur-spotlight': {
      title: 'Entrepreneur Spotlight: Success Stories',
      subtitle: 'Featuring inspiring entrepreneurs who are making a difference in their industries',
      author: 'Social Media Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-16',
      platform: 'Instagram',
      category: 'Spotlight',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      engagement: { likes: 890, comments: 32, shares: 56 },
      content: [
        {
          type: 'paragraph',
          text: 'This week we spotlight an incredible entrepreneur whose innovative approach has transformed their industry.',
        },
      ],
    },
    'innovation-showcase': {
      title: 'Innovation Showcase: Latest Developments',
      subtitle: 'Showcasing the latest innovations and technological breakthroughs',
      author: 'Social Media Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-14',
      platform: 'Twitter',
      category: 'Innovation',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      engagement: { likes: 2100, comments: 67, shares: 145 },
      content: [
        {
          type: 'paragraph',
          text: 'Innovation drives everything we do. Here are some of the latest developments that are shaping the future.',
        },
      ],
    },
    'community-engagement': {
      title: 'Community Engagement: Making a Difference',
      subtitle: "How we're giving back to our community and supporting local initiatives",
      author: 'Social Media Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-12',
      platform: 'Facebook',
      category: 'Community',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      engagement: { likes: 756, comments: 28, shares: 43 },
      content: [
        {
          type: 'paragraph',
          text: 'Community engagement is at the heart of what we do. Learn about our latest initiatives to support local communities.',
        },
      ],
    },
    'business-insights': {
      title: 'Business Insights: Industry Trends',
      subtitle: 'Expert analysis of current business trends and market developments',
      author: 'Social Media Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-10',
      platform: 'LinkedIn',
      category: 'Insights',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      engagement: { likes: 1450, comments: 89, shares: 112 },
      content: [
        {
          type: 'paragraph',
          text: 'Stay ahead of the curve with our expert insights into the latest business trends and market developments.',
        },
      ],
    },
    'company-updates': {
      title: 'Company Updates: Latest News',
      subtitle: 'Stay informed about the latest company news and announcements',
      author: 'Social Media Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-08',
      platform: 'Instagram',
      category: 'Updates',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      engagement: { likes: 623, comments: 19, shares: 34 },
      content: [
        {
          type: 'paragraph',
          text: "Keep up with all the latest company news and announcements. Here's what's been happening recently.",
        },
      ],
    },
  }

  return socialPosts[slug] || null
}

export default async function SocialPage({ params }: SocialPageProps) {
  const { slug } = await params
  const social = getSocialData(slug)

  if (!social) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Social Post Not Found</h1>
          <Link href="/news-media" className="text-blue-600 hover:underline">
            Back to News & Media
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Link
          href="/news-media"
          className="inline-flex items-center text-red-600 hover:text-red-700 mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to News & Media
        </Link>

        <div className="mb-8">
          <div className="flex items-center space-x-2 mb-4">
            <Badge className="bg-purple-600 text-white">{social.platform}</Badge>
            <Badge variant="outline">{social.category}</Badge>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {social.title}
          </h1>
          <p className="text-xl text-gray-600 mb-6 leading-relaxed">{social.subtitle}</p>

          <div className="flex items-center space-x-4 border-b border-gray-200 pb-6">
            <Image
              src={social.authorImage || '/placeholder.svg'}
              alt={social.author}
              width={50}
              height={50}
              className="rounded-full"
            />
            <div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="font-medium text-gray-900">{social.author}</span>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(social.publishDate).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="relative h-96 md:h-[500px] mb-8 rounded-lg overflow-hidden">
          <Image
            src={social.featuredImage || '/placeholder.svg'}
            alt={social.title}
            fill
            className="object-cover"
          />
        </div>

        <div className="prose prose-lg max-w-none mb-8">
          {social.content.map((block: any, index: number) => (
            <p key={index} className="text-gray-700 leading-relaxed mb-6 text-lg">
              {block.text}
            </p>
          ))}
        </div>

        {/* Social Engagement Stats */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">Engagement Stats</h3>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Heart className="w-5 h-5 text-red-500" />
                <span className="font-medium">{social.engagement.likes}</span>
                <span className="text-gray-600">Likes</span>
              </div>
              <div className="flex items-center space-x-2">
                <MessageCircle className="w-5 h-5 text-blue-500" />
                <span className="font-medium">{social.engagement.comments}</span>
                <span className="text-gray-600">Comments</span>
              </div>
              <div className="flex items-center space-x-2">
                <Share className="w-5 h-5 text-green-500" />
                <span className="font-medium">{social.engagement.shares}</span>
                <span className="text-gray-600">Shares</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Image
                src={social.authorImage || '/placeholder.svg'}
                alt={social.author}
                width={60}
                height={60}
                className="rounded-full"
              />
              <div>
                <h3 className="font-semibold text-gray-900">{social.author}</h3>
                <p className="text-gray-600">Social Media Manager</p>
              </div>
            </div>
            <Button asChild>
              <Link href="/news-media">View More Social Posts</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
