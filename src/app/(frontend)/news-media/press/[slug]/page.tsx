import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, Calendar, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface PressPageProps {
  params: Promise<{ slug: string }>
}

const getPressData = (slug: string) => {
  const pressReleases: Record<string, any> = {
    'strategic-partnership-announcement': {
      title: 'Strategic Partnership Announcement for Market Expansion',
      subtitle: 'New collaboration will expand market reach and enhance service offerings',
      author: 'Corporate Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-15',
      readTime: '4 min read',
      category: 'Partnership',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'We are pleased to announce a strategic partnership that will significantly enhance our market position and service capabilities. This collaboration represents a major milestone in our growth strategy.',
        },
        {
          type: 'paragraph',
          text: 'The partnership will enable us to expand into new markets while providing enhanced value to our existing customers through improved service offerings and innovative solutions.',
        },
      ],
    },
    'q4-financial-results': {
      title: 'Q4 Financial Results Exceed Expectations',
      subtitle: 'Strong performance across all business segments drives record quarterly results',
      author: 'Corporate Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-12',
      readTime: '5 min read',
      category: 'Financial',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'We are excited to report exceptional Q4 financial results that exceed all analyst expectations and demonstrate the strength of our business model.',
        },
      ],
    },
    'new-product-launch': {
      title: 'Revolutionary New Product Launch',
      subtitle: 'Introducing innovative solutions that will transform the industry landscape',
      author: 'Corporate Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-10',
      readTime: '6 min read',
      category: 'Product',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Today marks a significant milestone as we launch our revolutionary new product that represents years of research and development.',
        },
      ],
    },
    'executive-appointment': {
      title: 'Executive Leadership Appointment',
      subtitle: 'Welcoming new executive leadership to drive continued growth and innovation',
      author: 'Corporate Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-08',
      readTime: '3 min read',
      category: 'Personnel',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'We are pleased to announce the appointment of a new executive leader who will play a crucial role in our continued growth and strategic initiatives.',
        },
      ],
    },
    'market-expansion': {
      title: 'Strategic Market Expansion Initiative',
      subtitle: 'Expanding operations to serve new markets and customer segments',
      author: 'Corporate Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-05',
      readTime: '4 min read',
      category: 'Business',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Our strategic market expansion initiative will enable us to serve new customer segments and geographic markets with our proven solutions.',
        },
      ],
    },
    'corporate-sustainability-report': {
      title: 'Annual Corporate Sustainability Report',
      subtitle:
        'Demonstrating our commitment to environmental responsibility and sustainable business practices',
      author: 'Corporate Team',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-03',
      readTime: '7 min read',
      category: 'Sustainability',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Our annual sustainability report highlights our ongoing commitment to environmental responsibility and sustainable business practices across all operations.',
        },
      ],
    },
  }

  return pressReleases[slug] || null
}

export default async function PressPage({ params }: PressPageProps) {
  const { slug } = await params
  const press = getPressData(slug)

  if (!press) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Press Release Not Found</h1>
          <Link href="/news-media" className="text-blue-600 hover:underline">
            Back to News & Media
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Link
          href="/news-media"
          className="inline-flex items-center text-red-600 hover:text-red-700 mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to News & Media
        </Link>

        <div className="mb-8">
          <Badge className="bg-blue-600 text-white mb-4">{press.category}</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {press.title}
          </h1>
          <p className="text-xl text-gray-600 mb-6 leading-relaxed">{press.subtitle}</p>

          <div className="flex items-center space-x-4 border-b border-gray-200 pb-6">
            <Image
              src={press.authorImage || '/placeholder.svg'}
              alt={press.author}
              width={50}
              height={50}
              className="rounded-full"
            />
            <div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="font-medium text-gray-900">{press.author}</span>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(press.publishDate).toLocaleDateString()}
                </div>
                <span>{press.readTime}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="relative h-96 md:h-[500px] mb-8 rounded-lg overflow-hidden">
          <Image
            src={press.featuredImage || '/placeholder.svg'}
            alt={press.title}
            fill
            className="object-cover"
          />
        </div>

        <div className="prose prose-lg max-w-none">
          {press.content.map((block: any, index: number) => (
            <p key={index} className="text-gray-700 leading-relaxed mb-6 text-lg">
              {block.text}
            </p>
          ))}
        </div>

        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Image
                src={press.authorImage || '/placeholder.svg'}
                alt={press.author}
                width={60}
                height={60}
                className="rounded-full"
              />
              <div>
                <h3 className="font-semibold text-gray-900">{press.author}</h3>
                <p className="text-gray-600">Press Relations</p>
              </div>
            </div>
            <Button asChild>
              <Link href="/news-media">View More Press Releases</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
