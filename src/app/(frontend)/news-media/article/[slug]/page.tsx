import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, Calendar, User, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface ArticlePageProps {
  params: Promise<{ slug: string }>
}

const getArticleData = (slug: string) => {
  const articles: Record<string, any> = {
    'economic-policy-changes': {
      title: 'Breaking: Major Economic Policy Changes Announced',
      subtitle:
        'Government unveils comprehensive economic reform package aimed at boosting business growth and investment opportunities',
      author: 'Business Editor',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-20',
      readTime: '5 min read',
      category: 'Economics',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'In a landmark announcement today, the government unveiled a comprehensive economic reform package designed to stimulate business growth and attract foreign investment. The new policies represent the most significant economic changes in over a decade.',
        },
        {
          type: 'paragraph',
          text: 'The reform package includes tax incentives for small businesses, streamlined regulatory processes, and increased funding for infrastructure development. These measures are expected to create thousands of new jobs and boost economic growth by an estimated 3.5% over the next two years.',
        },
        {
          type: 'paragraph',
          text: 'Industry leaders have welcomed the announcement, with many expressing optimism about the potential for increased investment and expansion opportunities. The changes are set to take effect beginning next quarter.',
        },
      ],
    },
    'tech-earnings-report': {
      title: 'Tech Giants Report Record Quarterly Earnings',
      subtitle:
        'Leading technology companies exceed market expectations with unprecedented revenue growth across all sectors',
      author: 'Tech Reporter',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-18',
      readTime: '4 min read',
      category: 'Technology',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Major technology companies have reported exceptional quarterly earnings that far exceed analyst expectations, driven by strong demand for cloud services and artificial intelligence solutions.',
        },
        {
          type: 'paragraph',
          text: 'The robust performance across the tech sector reflects the ongoing digital transformation across industries and increased enterprise spending on technology infrastructure.',
        },
      ],
    },
    'market-recovery-signs': {
      title: 'Global Markets Show Strong Recovery Signs',
      subtitle:
        'International financial markets demonstrate resilience with sustained growth patterns emerging across major economies',
      author: 'Market Analyst',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-16',
      readTime: '6 min read',
      category: 'Markets',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Global financial markets are showing strong signs of recovery with sustained growth patterns emerging across major economies, signaling renewed investor confidence.',
        },
        {
          type: 'paragraph',
          text: 'The positive momentum is being driven by improved economic indicators, stabilizing inflation rates, and coordinated policy responses from central banks worldwide.',
        },
      ],
    },
    'investment-opportunities': {
      title: 'New Investment Opportunities Emerge in Developing Markets',
      subtitle:
        'Emerging markets present attractive investment prospects as economic conditions improve globally',
      author: 'Investment Analyst',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-14',
      readTime: '7 min read',
      category: 'Investment',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Developing markets are presenting new investment opportunities as economic conditions improve and regulatory frameworks become more investor-friendly.',
        },
        {
          type: 'paragraph',
          text: 'Portfolio managers are increasingly looking to diversify their holdings with exposure to high-growth emerging market economies that offer attractive risk-adjusted returns.',
        },
      ],
    },
    'sustainable-energy-transition': {
      title: 'Sustainable Energy Transition Accelerates Globally',
      subtitle:
        'Renewable energy adoption reaches new milestones as countries commit to carbon neutrality goals',
      author: 'Energy Reporter',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-12',
      readTime: '5 min read',
      category: 'Energy',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'The global transition to sustainable energy is accelerating at an unprecedented pace, with renewable energy sources now accounting for over 30% of global electricity generation.',
        },
      ],
    },
    'digital-transformation-trends': {
      title: 'Digital Transformation Trends Reshaping Industries',
      subtitle:
        'How artificial intelligence and automation are revolutionizing traditional business models',
      author: 'Digital Strategist',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-10',
      readTime: '6 min read',
      category: 'Digital',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Digital transformation continues to reshape industries as companies leverage artificial intelligence, automation, and data analytics to optimize operations and enhance customer experiences.',
        },
      ],
    },
  }

  return articles[slug] || null
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const { slug } = await params
  const article = getArticleData(slug)

  if (!article) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h1>
          <Link href="/news-media" className="text-blue-600 hover:underline">
            Back to News & Media
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Link
          href="/news-media"
          className="inline-flex items-center text-red-600 hover:text-red-700 mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to News & Media
        </Link>

        <div className="mb-8">
          <Badge className="bg-red-600 text-white mb-4">{article.category}</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {article.title}
          </h1>
          <p className="text-xl text-gray-600 mb-6 leading-relaxed">{article.subtitle}</p>

          <div className="flex items-center space-x-4 border-b border-gray-200 pb-6">
            <Image
              src={article.authorImage || '/placeholder.svg'}
              alt={article.author}
              width={50}
              height={50}
              className="rounded-full"
            />
            <div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="font-medium text-gray-900">{article.author}</span>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(article.publishDate).toLocaleDateString()}
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {article.readTime}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="relative h-96 md:h-[500px] mb-8 rounded-lg overflow-hidden">
          <Image
            src={article.featuredImage || '/placeholder.svg'}
            alt={article.title}
            fill
            className="object-cover"
          />
        </div>

        <div className="prose prose-lg max-w-none">
          {article.content.map((block: any, index: number) => (
            <p key={index} className="text-gray-700 leading-relaxed mb-6 text-lg">
              {block.text}
            </p>
          ))}
        </div>

        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Image
                src={article.authorImage || '/placeholder.svg'}
                alt={article.author}
                width={60}
                height={60}
                className="rounded-full"
              />
              <div>
                <h3 className="font-semibold text-gray-900">{article.author}</h3>
                <p className="text-gray-600">News Reporter</p>
              </div>
            </div>
            <Button asChild>
              <Link href="/news-media">View More Articles</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
