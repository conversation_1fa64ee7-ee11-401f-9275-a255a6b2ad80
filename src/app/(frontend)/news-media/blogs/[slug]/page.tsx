import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, Calendar, Facebook, Twitter, Linkedin, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface BlogPageProps {
  params: Promise<{ slug: string }>
}

const getBlogData = (slug: string) => {
  const blogs: Record<string, any> = {
    'future-of-business-trends': {
      title: "The Future of Business: Trends Shaping Tomorrow's Economy",
      subtitle:
        'Explore the key trends and innovations that will define the business landscape in the coming decade',
      author: 'Business Analyst',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-12',
      readTime: '5 min read',
      category: 'Business Strategy',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'The business world is evolving at an unprecedented pace, driven by technological innovation, changing consumer expectations, and global economic shifts. As we look toward the future, several key trends are emerging that will fundamentally reshape how companies operate and compete.',
        },
        {
          type: 'image',
          src: '/placeholder.svg?height=400&width=800&query=future business technology',
          alt: 'Future business technology trends',
          caption: 'Emerging technologies are reshaping the business landscape',
        },
        {
          type: 'paragraph',
          text: 'Digital transformation continues to be a driving force, with artificial intelligence, machine learning, and automation becoming integral to business operations. Companies that embrace these technologies are seeing significant improvements in efficiency, customer experience, and competitive advantage.',
        },
        {
          type: 'quote',
          text: 'The future belongs to organizations that can adapt quickly, embrace innovation, and put their customers at the center of everything they do.',
          author: 'Leading Business Strategist',
        },
        {
          type: 'paragraph',
          text: 'Sustainability and social responsibility are no longer optional considerations but essential components of successful business strategies. Consumers and investors increasingly favor companies that demonstrate genuine commitment to environmental and social impact.',
        },
      ],
    },
    'leadership-digital-age': {
      title: 'Leadership in the Digital Age: Adapting to Change',
      subtitle:
        'How modern leaders can navigate digital transformation while maintaining human connection and purpose',
      author: 'Leadership Expert',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-10',
      readTime: '7 min read',
      category: 'Leadership',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: "Digital transformation has fundamentally changed the nature of leadership. Today's leaders must navigate complex technological landscapes while maintaining the human elements that drive organizational success.",
        },
        {
          type: 'paragraph',
          text: 'The most effective digital-age leaders combine technological fluency with emotional intelligence, creating environments where innovation thrives while people feel valued and connected to purpose.',
        },
      ],
    },
    'essential-business-strategies': {
      title: 'Essential Business Strategies for Modern Entrepreneurs',
      subtitle: "Key strategies every entrepreneur needs to succeed in today's competitive market",
      author: 'Business Expert',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-08',
      readTime: '6 min read',
      category: 'Strategy',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Modern entrepreneurship requires a strategic approach that combines innovation with proven business fundamentals...',
        },
      ],
    },
    'navigating-digital-transformation': {
      title: 'Navigating Digital Transformation',
      subtitle:
        'A comprehensive guide to successfully implementing digital transformation in your organization',
      author: 'Tech Strategist',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-06',
      readTime: '8 min read',
      category: 'Technology',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Digital transformation is more than just adopting new technologies...',
        },
      ],
    },
    'sustainable-business-practices': {
      title: 'Sustainable Business Practices',
      subtitle: 'How to build a sustainable business that benefits both profit and planet',
      author: 'Sustainability Expert',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-04',
      readTime: '7 min read',
      category: 'Sustainability',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: "Sustainability is no longer just a buzzword - it's a business imperative...",
        },
      ],
    },
    'future-of-workplace': {
      title: 'Future of Workplace',
      subtitle: 'Exploring how remote work and hybrid models are reshaping the modern workplace',
      author: 'HR Specialist',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2024-01-02',
      readTime: '5 min read',
      category: 'Workplace',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'The workplace has undergone dramatic changes in recent years...',
        },
      ],
    },
    'entrepreneur-success-stories': {
      title: 'Entrepreneur Success Stories',
      subtitle: 'Inspiring stories from entrepreneurs who built successful businesses from scratch',
      author: 'Success Coach',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2023-12-30',
      readTime: '9 min read',
      category: 'Success Stories',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Behind every successful business is an entrepreneur who dared to dream...',
        },
      ],
    },
    'technology-transforming-business': {
      title: 'Technology Transforming Business',
      subtitle: 'How emerging technologies are revolutionizing traditional business models',
      author: 'Tech Analyst',
      authorImage: '/placeholder.svg?height=50&width=50',
      publishDate: '2023-12-28',
      readTime: '6 min read',
      category: 'Technology',
      featuredImage: '/placeholder.svg?height=600&width=1200',
      content: [
        {
          type: 'paragraph',
          text: 'Technology continues to disrupt and transform every aspect of business...',
        },
      ],
    },
  }

  return blogs[slug] || null
}

export default async function BlogPage({ params }: BlogPageProps) {
  const { slug } = await params
  const blog = getBlogData(slug)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Link
          href="/news-media?tab=blogs"
          className="inline-flex items-center text-red-600 hover:text-red-700 mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Blogs
        </Link>

        {/* Blog Header */}
        <div className="mb-8">
          <Badge className="bg-green-600 text-white mb-4">{blog.category}</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {blog.title}
          </h1>
          <p className="text-xl text-gray-600 mb-6 leading-relaxed">{blog.subtitle}</p>

          {/* Blog Meta */}
          <div className="flex items-center justify-between border-b border-gray-200 pb-6">
            <div className="flex items-center space-x-4">
              <Image
                src={blog.authorImage || '/placeholder.svg'}
                alt={blog.author}
                width={50}
                height={50}
                className="rounded-full"
              />
              <div>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span className="font-medium text-gray-900">{blog.author}</span>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(blog.publishDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {blog.readTime}
                  </div>
                </div>
              </div>
            </div>

            {/* Share Buttons */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 mr-2">Share:</span>
              <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                <Facebook className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="text-blue-400 hover:bg-blue-50">
                <Twitter className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="text-blue-700 hover:bg-blue-50">
                <Linkedin className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Featured Image */}
        <div className="relative h-96 md:h-[500px] mb-8 rounded-lg overflow-hidden">
          <Image
            src={blog.featuredImage || '/placeholder.svg'}
            alt={blog.title}
            fill
            className="object-cover"
          />
        </div>

        {/* Blog Content */}
        <div className="prose prose-lg max-w-none">
          {blog.content.map((block: any, index: number) => {
            switch (block.type) {
              case 'paragraph':
                return (
                  <p key={index} className="text-gray-700 leading-relaxed mb-6 text-lg">
                    {block.text}
                  </p>
                )
              case 'image':
                return (
                  <div key={index} className="my-8">
                    <div className="relative h-80 md:h-96 rounded-lg overflow-hidden mb-3">
                      <Image
                        src={block.src || '/placeholder.svg'}
                        alt={block.alt}
                        fill
                        className="object-cover"
                      />
                    </div>
                    {block.caption && (
                      <p className="text-sm text-gray-500 italic text-center">{block.caption}</p>
                    )}
                  </div>
                )
              case 'quote':
                return (
                  <blockquote
                    key={index}
                    className="border-l-4 border-green-600 pl-6 my-8 bg-gray-50 p-6 rounded-r-lg"
                  >
                    <p className="text-xl italic text-gray-800 mb-2">"{block.text}"</p>
                    <cite className="text-sm text-gray-600 font-medium">— {block.author}</cite>
                  </blockquote>
                )
              default:
                return null
            }
          })}
        </div>

        {/* Related Blog Posts */}
        <div className="mt-16 pt-8 border-t border-gray-200">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Related Blog Posts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[1, 2].map((item) => (
              <Card key={item} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative h-48">
                  <Image
                    src={`/placeholder.svg?height=200&width=400&query=related blog post ${item}`}
                    alt={`Related blog post ${item}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <Badge className="bg-green-600 text-white mb-2">Blog</Badge>
                  <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    Related Business Insights and Strategic Analysis
                  </h4>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>3 days ago</span>
                    <span>Blog Editor</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
