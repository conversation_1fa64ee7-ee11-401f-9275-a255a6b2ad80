'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import {
  Twitter,
  Facebook,
  Linkedin,
  Instagram,
  Phone,
  Mail,
  Calendar,
  MapPin,
  Clock,
  ChevronDown,
} from 'lucide-react'

// Replace Button component with native button
const Button = ({ children, variant = 'default', className = '', onClick, disabled, ...props }) => {
  const baseClasses =
    'px-4 py-2 rounded-md font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2'
  const variantClasses =
    variant === 'outline'
      ? 'border border-current bg-transparent hover:bg-current hover:text-white'
      : 'bg-current text-white hover:opacity-90'

  return (
    <button
      className={`${baseClasses} ${variantClasses} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  )
}

// Replace Select components with native select
const Select = ({ value, onValueChange, children, className = '' }) => {
  return (
    <div className={`relative ${className}`}>
      <select
        value={value}
        onChange={(e) => onValueChange(e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none pr-8"
      >
        {children}
      </select>
      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
    </div>
  )
}

const SelectTrigger = ({ children, className }) => children
const SelectValue = ({ placeholder }) => null
const SelectContent = ({ children }) => children
const SelectItem = ({ value, children }) => <option value={value}>{children}</option>

// Replace Card components with native divs
const Card = ({ children, className = '', onClick, style }) => (
  <div
    className={`bg-white rounded-lg shadow-md border border-gray-200 ${className}`}
    onClick={onClick}
    style={style}
  >
    {children}
  </div>
)

const CardContent = ({ children, className = '' }) => <div className={className}>{children}</div>

// Replace Badge component with native span
const Badge = ({ children, className = '' }) => (
  <span
    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${className}`}
  >
    {children}
  </span>
)

interface Event {
  id: number
  title: string
  date: string
  time: string
  location: string
  description: string
  type: 'upcoming' | 'past'
}

interface Speaker {
  id: number
  name: string
  title: string
  organization: string
  topic: string
  category: 'main' | 'keynote' | 'other'
  image: string
  bio: string
  events: Event[]
  social: {
    twitter?: string
    facebook?: string
    linkedin?: string
    instagram?: string
  }
}

const speakers: Speaker[] = [
  {
    id: 1,
    name: 'Thomas May',
    title: 'Founder of FormM',
    organization: 'FormM',
    topic: 'Innovation',
    category: 'main',
    image: '/public/alex1_converted.svg',
    bio: 'Thomas is a visionary leader with over 15 years of experience in product development and innovation. He founded FormM to revolutionize how companies approach digital transformation.',
    events: [
      {
        id: 1,
        title: 'Digital Transformation Summit 2024',
        date: '2024-03-15',
        time: '10:00 AM',
        location: 'Main Auditorium',
        description: 'Keynote on the future of digital transformation',
        type: 'upcoming',
      },
      {
        id: 2,
        title: 'Innovation Leadership Workshop',
        date: '2024-04-22',
        time: '2:00 PM',
        location: 'Workshop Hall B',
        description: 'Interactive session on innovation methodologies',
        type: 'upcoming',
      },
      {
        id: 3,
        title: 'Tech Startup Accelerator',
        date: '2024-05-10',
        time: '9:00 AM',
        location: 'Innovation Center',
        description: 'Mentoring session for early-stage startups',
        type: 'upcoming',
      },
      {
        id: 4,
        title: 'Product Innovation Conference 2023',
        date: '2023-11-20',
        time: '2:00 PM',
        location: 'Convention Center',
        description: 'Presented on breakthrough innovation strategies',
        type: 'past',
      },
      {
        id: 5,
        title: 'Digital Leaders Forum',
        date: '2023-09-15',
        time: '11:00 AM',
        location: 'Business Hub',
        description: 'Panel discussion on digital leadership',
        type: 'past',
      },
      {
        id: 6,
        title: 'Innovation Expo 2023',
        date: '2023-07-08',
        time: '3:30 PM',
        location: 'Expo Hall',
        description: "Showcased FormM's latest innovations",
        type: 'past',
      },
    ],
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: 2,
    name: 'Anna Harrison',
    title: 'Product Designer',
    organization: 'TechCorp',
    topic: 'Design',
    category: 'keynote',
    image: '/public/alex1_converted.svg',
    bio: 'Anna is a creative product designer who specializes in user experience and interface design. She has worked with leading tech companies to create intuitive digital experiences.',
    events: [
      {
        id: 7,
        title: 'UX Design Masterclass',
        date: '2024-03-22',
        time: '9:00 AM',
        location: 'Design Studio',
        description: 'Advanced UX design principles and practices',
        type: 'upcoming',
      },
      {
        id: 8,
        title: 'Design Systems Workshop',
        date: '2024-04-18',
        time: '1:00 PM',
        location: 'Creative Lab',
        description: 'Building scalable design systems',
        type: 'upcoming',
      },
      {
        id: 9,
        title: 'User Research Bootcamp',
        date: '2024-05-25',
        time: '10:00 AM',
        location: 'Research Center',
        description: 'Comprehensive user research methodologies',
        type: 'upcoming',
      },
      {
        id: 10,
        title: 'Design Thinking Workshop',
        date: '2024-01-15',
        time: '11:00 AM',
        location: 'Creative Space',
        description: 'Hands-on design thinking methodology',
        type: 'past',
      },
      {
        id: 11,
        title: 'UI/UX Conference 2023',
        date: '2023-10-12',
        time: '2:30 PM',
        location: 'Design Center',
        description: 'Keynote on the future of user experience',
        type: 'past',
      },
      {
        id: 12,
        title: 'Creative Design Summit',
        date: '2023-08-20',
        time: '4:00 PM',
        location: 'Art Gallery',
        description: 'Exploring creativity in digital design',
        type: 'past',
      },
      {
        id: 13,
        title: 'Product Design Workshop',
        date: '2023-06-05',
        time: '9:30 AM',
        location: 'TechCorp HQ',
        description: 'Internal workshop on product design best practices',
        type: 'past',
      },
    ],
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: 3,
    name: 'Thomas Powell',
    title: 'Product Lead',
    organization: 'InnovateCo',
    topic: 'Leadership',
    category: 'main',
    image: '/public/alex1_converted.svg',
    bio: "Thomas Powell leads product strategy at InnovateCo, focusing on emerging technologies and market trends. He's passionate about building products that solve real-world problems.",
    events: [
      {
        id: 14,
        title: 'Product Leadership Forum',
        date: '2024-04-10',
        time: '1:00 PM',
        location: 'Conference Room A',
        description: 'Strategic product leadership in tech companies',
        type: 'upcoming',
      },
      {
        id: 15,
        title: 'Agile Product Management',
        date: '2024-05-15',
        time: '3:00 PM',
        location: 'Agile Center',
        description: 'Advanced agile methodologies for product teams',
        type: 'upcoming',
      },
      {
        id: 16,
        title: 'Product Strategy Workshop',
        date: '2023-12-08',
        time: '10:00 AM',
        location: 'Strategy Room',
        description: 'Developing winning product strategies',
        type: 'past',
      },
      {
        id: 17,
        title: 'Leadership Excellence Summit',
        date: '2023-09-22',
        time: '2:00 PM',
        location: 'Leadership Hall',
        description: 'Building high-performing product teams',
        type: 'past',
      },
      {
        id: 18,
        title: 'Tech Leadership Conference',
        date: '2023-07-14',
        time: '11:30 AM',
        location: 'Tech Arena',
        description: 'Panel on emerging technology trends',
        type: 'past',
      },
    ],
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: 4,
    name: 'Marry April',
    title: 'Product Manager',
    organization: 'StartupHub',
    topic: 'Strategy',
    category: 'other',
    image: '/public/alex1_converted.svg',
    bio: 'Marry is an experienced product manager with a track record of launching successful products. She specializes in agile methodologies and cross-functional team leadership.',
    events: [
      {
        id: 19,
        title: 'Agile Product Management',
        date: '2024-03-28',
        time: '3:00 PM',
        location: 'Meeting Room 1',
        description: 'Best practices in agile product management',
        type: 'upcoming',
      },
      {
        id: 20,
        title: 'Product Launch Strategies',
        date: '2024-04-30',
        time: '11:00 AM',
        location: 'Launch Pad',
        description: 'Successful product launch methodologies',
        type: 'upcoming',
      },
      {
        id: 21,
        title: 'Startup Product Workshop',
        date: '2023-11-18',
        time: '1:30 PM',
        location: 'StartupHub',
        description: 'Product management for early-stage startups',
        type: 'past',
      },
      {
        id: 22,
        title: 'Cross-functional Team Leadership',
        date: '2023-08-25',
        time: '9:00 AM',
        location: 'Team Building Center',
        description: 'Leading diverse product teams effectively',
        type: 'past',
      },
    ],
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: 5,
    name: 'John May',
    title: 'Founder',
    organization: 'TechStart',
    topic: 'Entrepreneurship',
    category: 'keynote',
    image: '/public/alex1_converted.svg',
    bio: "John is a serial entrepreneur who has founded multiple successful startups. He's passionate about mentoring the next generation of entrepreneurs and sharing insights on building scalable businesses.",
    events: [
      {
        id: 23,
        title: 'Startup Success Stories',
        date: '2024-03-18',
        time: '4:00 PM',
        location: 'Main Stage',
        description: 'Journey from idea to successful startup',
        type: 'upcoming',
      },
      {
        id: 24,
        title: 'Entrepreneurship Masterclass',
        date: '2024-05-08',
        time: '10:00 AM',
        location: 'Entrepreneur Hub',
        description: 'Advanced entrepreneurship strategies',
        type: 'upcoming',
      },
      {
        id: 25,
        title: 'Venture Capital Panel',
        date: '2024-06-12',
        time: '2:30 PM',
        location: 'Investment Center',
        description: 'Securing funding for your startup',
        type: 'upcoming',
      },
      {
        id: 26,
        title: 'Entrepreneurship Bootcamp',
        date: '2024-01-25',
        time: '10:00 AM',
        location: 'Startup Hub',
        description: 'Intensive entrepreneurship training program',
        type: 'past',
      },
      {
        id: 27,
        title: 'Startup Pitch Competition',
        date: '2023-12-15',
        time: '6:00 PM',
        location: 'Pitch Arena',
        description: 'Judged startup pitch competition',
        type: 'past',
      },
      {
        id: 28,
        title: 'Business Model Innovation',
        date: '2023-10-30',
        time: '1:00 PM',
        location: 'Innovation Lab',
        description: 'Workshop on innovative business models',
        type: 'past',
      },
      {
        id: 29,
        title: 'Scaling Startups Conference',
        date: '2023-08-18',
        time: '3:00 PM',
        location: 'Scale Center',
        description: 'Strategies for scaling startup operations',
        type: 'past',
      },
      {
        id: 30,
        title: "Founder's Journey Panel",
        date: '2023-06-22',
        time: '11:00 AM',
        location: "Founder's Hall",
        description: 'Personal stories from successful founders',
        type: 'past',
      },
    ],
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: 6,
    name: 'Sarah Chen',
    title: 'CTO',
    organization: 'DataFlow',
    topic: 'Technology',
    category: 'main',
    image: '/public/alex1_converted.svg',
    bio: 'Sarah is a technology leader with expertise in AI and machine learning. She has led engineering teams at several Fortune 500 companies.',
    events: [
      {
        id: 9,
        title: 'AI & Machine Learning Summit',
        date: '2024-04-05',
        time: '11:00 AM',
        location: 'Tech Theater',
        description: 'Latest trends in AI and ML technologies',
        type: 'upcoming',
      },
    ],
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: 7,
    name: 'Michael Rodriguez',
    title: 'VP of Engineering',
    organization: 'CloudTech',
    topic: 'Cloud Computing',
    category: 'other',
    image: '/public/alex1_converted.svg',
    bio: 'Michael leads engineering teams in building scalable cloud infrastructure. He has expertise in distributed systems and microservices architecture.',
    events: [
      {
        id: 10,
        title: 'Cloud Architecture Workshop',
        date: '2024-03-30',
        time: '2:00 PM',
        location: 'Tech Lab',
        description: 'Building scalable cloud solutions',
        type: 'upcoming',
      },
    ],
    social: { twitter: '#', facebook: '#', linkedin: '#', instagram: '#' },
  },
  {
    id: 8,
    name: 'Lisa Wang',
    title: 'Head of Product',
    organization: 'FinanceFlow',
    topic: 'Fintech',
    category: 'keynote',
    image: '/public/alex1_converted.svg',
    bio: 'Lisa specializes in fintech product development with a focus on user experience and regulatory compliance in financial services.',
    events: [
      {
        id: 11,
        title: 'Fintech Innovation Panel',
        date: '2024-04-12',
        time: '3:30 PM',
        location: 'Finance Hall',
        description: 'Future of financial technology',
        type: 'upcoming',
      },
    ],
    social: { twitter: '#', facebook: '#', linkedin: '#', instagram: '#' },
  },
  {
    id: 9,
    name: 'David Thompson',
    title: 'Chief Innovation Officer',
    organization: 'RetailNext',
    topic: 'Retail Innovation',
    category: 'other',
    image: '/public/alex1_converted.svg',
    bio: 'David drives innovation in retail technology, focusing on omnichannel experiences and digital transformation strategies.',
    events: [
      {
        id: 12,
        title: 'Retail Tech Showcase',
        date: '2024-03-25',
        time: '1:30 PM',
        location: 'Retail Space',
        description: 'Latest innovations in retail technology',
        type: 'upcoming',
      },
    ],
    social: { twitter: '#', facebook: '#', linkedin: '#', instagram: '#' },
  },
  {
    id: 10,
    name: 'Rachel Green',
    title: 'Data Science Director',
    organization: 'AnalyticsPro',
    topic: 'Data Science',
    category: 'main',
    image: '/public/alex1_converted.svg',
    bio: 'Rachel leads data science initiatives, specializing in machine learning applications for business intelligence and predictive analytics.',
    events: [
      {
        id: 13,
        title: 'Data Science Bootcamp',
        date: '2024-04-08',
        time: '9:00 AM',
        location: 'Analytics Center',
        description: 'Comprehensive data science training',
        type: 'upcoming',
      },
    ],
    social: { twitter: '#', facebook: '#', linkedin: '#', instagram: '#' },
  },
  {
    id: 11,
    name: 'James Wilson',
    title: 'Startup Mentor',
    organization: 'VentureHub',
    topic: 'Mentorship',
    category: 'other',
    image: '/public/alex1_converted.svg',
    bio: 'James mentors early-stage startups and has successfully guided over 50 companies through their growth phases.',
    events: [
      {
        id: 14,
        title: 'Mentorship Circle',
        date: '2024-03-20',
        time: '5:00 PM',
        location: 'Networking Lounge',
        description: 'Startup mentorship and networking session',
        type: 'upcoming',
      },
    ],
    social: { twitter: '#', facebook: '#', linkedin: '#', instagram: '#' },
  },
  {
    id: 12,
    name: 'Sofia Martinez',
    title: 'UX Research Lead',
    organization: 'DesignLab',
    topic: 'User Research',
    category: 'other',
    image: '/public/alex1_converted.svg',
    bio: 'Sofia leads user research initiatives, focusing on human-centered design and accessibility in digital products.',
    events: [
      {
        id: 15,
        title: 'User Research Methods',
        date: '2024-04-15',
        time: '10:30 AM',
        location: 'Research Lab',
        description: 'Advanced user research methodologies',
        type: 'upcoming',
      },
    ],
    social: { twitter: '#', facebook: '#', linkedin: '#', instagram: '#' },
  },
  {
    id: 13,
    name: 'Alex Kim',
    title: 'Blockchain Developer',
    organization: 'CryptoTech',
    topic: 'Blockchain',
    category: 'keynote',
    image: '/public/alex1_converted.svg',
    bio: 'Alex is a blockchain expert specializing in smart contracts and decentralized applications for enterprise solutions.',
    events: [
      {
        id: 16,
        title: 'Blockchain Revolution',
        date: '2024-04-20',
        time: '2:30 PM',
        location: 'Crypto Arena',
        description: 'The future of blockchain technology',
        type: 'upcoming',
      },
    ],
    social: { twitter: '#', facebook: '#', linkedin: '#', instagram: '#' },
  },
  {
    id: 14,
    name: 'Emma Davis',
    title: 'Marketing Director',
    organization: 'GrowthCo',
    topic: 'Growth Marketing',
    category: 'other',
    image: '/public/alex1_converted.svg',
    bio: 'Emma drives growth marketing strategies with expertise in digital marketing, customer acquisition, and brand development.',
    events: [
      {
        id: 17,
        title: 'Growth Marketing Strategies',
        date: '2024-03-27',
        time: '4:30 PM',
        location: 'Marketing Hub',
        description: 'Effective growth marketing techniques',
        type: 'upcoming',
      },
    ],
    social: { twitter: '#', facebook: '#', linkedin: '#', instagram: '#' },
  },
]

const getCategoryLabel = (category: string) => {
  switch (category) {
    case 'main':
      return 'Main Speaker'
    case 'keynote':
      return 'Keynote Speaker'
    case 'other':
      return 'Speaker'
    default:
      return 'Speaker'
  }
}

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'main':
      return 'bg-[#159147] text-white'
    case 'keynote':
      return 'bg-[#E8B32C] text-black'
    case 'other':
      return 'bg-[#81B1DB] text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}

const getCategoryTitle = (category: string) => {
  switch (category) {
    case 'main':
      return 'MAIN SPEAKERS'
    case 'keynote':
      return 'KEYNOTE SPEAKERS'
    case 'other':
      return 'OTHER SPEAKERS'
    default:
      return 'SPEAKERS'
  }
}

const SpeakerCard = ({
  speaker,
  isSelected,
  hasSelectedSpeaker,
  onSpeakerClick,
  formatDate,
}: {
  speaker: Speaker
  isSelected: boolean
  hasSelectedSpeaker: boolean
  onSpeakerClick: (speaker: Speaker) => void
  formatDate: (dateString: string) => string
}) => {
  const isCompact = hasSelectedSpeaker && !isSelected
  const [activeEventTab, setActiveEventTab] = useState('upcoming')

  return (
    <div
      className={`
        transition-all duration-500 ease-in-out transform
        ${
          isSelected
            ? 'col-span-1 sm:col-span-2 lg:col-span-2 xl:col-span-2 scale-105 z-10'
            : isCompact
              ? 'scale-90 opacity-80'
              : 'scale-100 opacity-100'
        }
      `}
      style={{
        transformOrigin: 'center',
      }}
    >
      <Card
        className={`
          cursor-pointer transition-all duration-500 ease-in-out
          ${isSelected ? 'ring-2 ring-[#159147] shadow-2xl' : 'hover:shadow-lg hover:scale-105'}
          ${isCompact ? 'hover:scale-95' : ''}
        `}
        onClick={() => onSpeakerClick(speaker)}
        style={{
          backgroundColor: isSelected ? '#159147' : 'white',
          color: isSelected ? 'white' : 'inherit',
        }}
      >
        <CardContent
          className={`p-6 flex flex-col ${isSelected ? 'sm:flex-row sm:items-start sm:gap-6' : 'text-center'}`}
        >
          {/* Profile Image */}
          <div
            className={`relative mx-auto mb-4 flex-shrink-0 ${isSelected ? 'lg:mx-0 lg:mb-0' : ''}`}
          >
            <div
              className={`relative transition-all duration-500 ${isSelected ? 'w-32 h-32 sm:w-40 sm:h-40' : 'w-24 h-24'}`}
            >
              <Image
                src={speaker.image || '/placeholder.svg'}
                alt={speaker.name}
                fill
                className="rounded-full object-cover"
              />
            </div>
            {isSelected && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-[#E8B32C] rounded-full animate-pulse"></div>
            )}

            {/* Social Media Icons - positioned below profile image when expanded */}
            {isSelected && (
              <div className="flex justify-center space-x-3 mt-4">
                <Twitter className="w-6 h-6 text-gray-200 hover:text-[#81B1DB] cursor-pointer transition-all duration-300" />
                <Facebook className="w-6 h-6 text-gray-200 hover:text-[#81B1DB] cursor-pointer transition-all duration-300" />
                <Linkedin className="w-6 h-6 text-gray-200 hover:text-[#81B1DB] cursor-pointer transition-all duration-300" />
                <Instagram className="w-6 h-6 text-gray-200 hover:text-[#C86E36] cursor-pointer transition-all duration-300" />
              </div>
            )}
          </div>

          {/* Content */}
          <div className={`flex-1 ${isSelected ? 'sm:text-left' : 'text-center'}`}>
            {/* Category Badge */}
            <div className="mb-3">
              <Badge className={`${getCategoryColor(speaker.category)} text-xs font-medium`}>
                {getCategoryLabel(speaker.category)}
              </Badge>
            </div>

            <h3
              className={`font-semibold mb-1 transition-all duration-300 ${isSelected ? 'text-2xl sm:text-3xl' : 'text-xl'}`}
            >
              {speaker.name}
            </h3>
            <p
              className={`mb-4 transition-all duration-300 ${isSelected ? 'text-gray-100 text-lg' : 'text-gray-600'}`}
            >
              [{speaker.title}]
            </p>

            {/* Expanded Content with Tabs */}
            {isSelected && (
              <div className="animate-fadeIn">
                <p className="text-gray-100 mb-6 leading-relaxed text-sm sm:text-base">
                  {speaker.bio}
                </p>
                <div className="space-y-2 text-sm text-gray-200 mb-6">
                  <p>
                    <span className="font-semibold text-[#E8B32C]">Organization:</span>{' '}
                    {speaker.organization}
                  </p>
                  <p>
                    <span className="font-semibold text-[#E8B32C]">Topic:</span> {speaker.topic}
                  </p>
                </div>

                <div className="w-full">
                  <div
                    className="grid w-full grid-cols-2 bg-white/10 h-auto"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setActiveEventTab('upcoming')
                      }}
                      className={`text-xs sm:text-sm py-2 px-2 sm:px-4 transition-colors ${
                        activeEventTab === 'upcoming'
                          ? 'bg-[#E8B32C] text-black'
                          : 'text-white hover:bg-white/10'
                      }`}
                    >
                      <span className="hidden sm:inline">Upcoming Events</span>
                      <span className="sm:hidden">Upcoming</span>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setActiveEventTab('past')
                      }}
                      className={`text-xs sm:text-sm py-2 px-2 sm:px-4 transition-colors ${
                        activeEventTab === 'past'
                          ? 'bg-[#E8B32C] text-black'
                          : 'text-white hover:bg-white/10'
                      }`}
                    >
                      <span className="hidden sm:inline">Past Events</span>
                      <span className="sm:hidden">Past</span>
                    </button>
                  </div>

                  {activeEventTab === 'upcoming' && (
                    <div className="mt-4" onClick={(e) => e.stopPropagation()}>
                      <div className="space-y-3 max-h-40 overflow-y-auto pr-2">
                        {speaker.events.filter((event) => event.type === 'upcoming').length > 0 ? (
                          speaker.events
                            .filter((event) => event.type === 'upcoming')
                            .map((event) => (
                              <div key={event.id} className="bg-white/10 rounded-lg p-4">
                                <div className="flex items-start gap-3">
                                  <Calendar className="w-5 h-5 text-[#E8B32C] mt-1 flex-shrink-0" />
                                  <div className="flex-1">
                                    <h4 className="font-semibold text-white mb-1">{event.title}</h4>
                                    <p className="text-gray-200 text-sm mb-2">
                                      {event.description}
                                    </p>
                                    <div className="flex flex-wrap gap-4 text-xs text-gray-300">
                                      <div className="flex items-center gap-1">
                                        <Calendar className="w-3 h-3" />
                                        {formatDate(event.date)}
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        {event.time}
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <MapPin className="w-3 h-3" />
                                        {event.location}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))
                        ) : (
                          <p className="text-gray-300 text-center py-4">
                            No upcoming events scheduled
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {activeEventTab === 'past' && (
                    <div className="mt-4" onClick={(e) => e.stopPropagation()}>
                      <div className="space-y-3 max-h-40 overflow-y-auto pr-2">
                        {speaker.events.filter((event) => event.type === 'past').length > 0 ? (
                          speaker.events
                            .filter((event) => event.type === 'past')
                            .map((event) => (
                              <div key={event.id} className="bg-white/10 rounded-lg p-4">
                                <div className="flex items-start gap-3">
                                  <Calendar className="w-5 h-5 text-[#E8B32C] mt-1 flex-shrink-0" />
                                  <div className="flex-1">
                                    <h4 className="font-semibold text-white mb-1">{event.title}</h4>
                                    <p className="text-gray-200 text-sm mb-2">
                                      {event.description}
                                    </p>
                                    <div className="flex flex-wrap gap-4 text-xs text-gray-300">
                                      <div className="flex items-center gap-1">
                                        <Calendar className="w-3 h-3" />
                                        {formatDate(event.date)}
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        {event.time}
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <MapPin className="w-3 h-3" />
                                        {event.location}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))
                        ) : (
                          <p className="text-gray-300 text-center py-4">No past events recorded</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Social Media Icons for non-expanded profiles */}
            {!isSelected && (
              <div className="flex justify-center space-x-3 mt-4">
                <Twitter className="w-5 h-5 text-gray-400 hover:text-[#81B1DB] cursor-pointer transition-all duration-300" />
                <Facebook className="w-5 h-5 text-gray-400 hover:text-[#81B1DB] cursor-pointer transition-all duration-300" />
                <Linkedin className="w-5 h-5 text-gray-400 hover:text-[#81B1DB] cursor-pointer transition-all duration-300" />
                <Instagram className="w-5 h-5 text-gray-400 hover:text-[#C86E36] cursor-pointer transition-all duration-300" />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function SpeakersPage() {
  const [selectedSpeaker, setSelectedSpeaker] = useState<Speaker | null>(null)
  const [speakerFilter, setSpeakerFilter] = useState<string>('all')
  const [organizationFilter, setOrganizationFilter] = useState<string>('all')
  const [topicFilter, setTopicFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [speakersPerPage] = useState(12)

  useEffect(() => {
    setCurrentPage(1)
    setSelectedSpeaker(null)
  }, [speakerFilter, organizationFilter, topicFilter, categoryFilter])

  const filteredSpeakers = speakers.filter((speaker) => {
    return (
      (speakerFilter === 'all' ||
        speaker.name.toLowerCase().includes(speakerFilter.toLowerCase())) &&
      (organizationFilter === 'all' || speaker.organization === organizationFilter) &&
      (topicFilter === 'all' || speaker.topic === topicFilter) &&
      (categoryFilter === 'all' || speaker.category === categoryFilter)
    )
  })

  // Group speakers by category
  const groupedSpeakers = {
    main: filteredSpeakers.filter((speaker) => speaker.category === 'main'),
    keynote: filteredSpeakers.filter((speaker) => speaker.category === 'keynote'),
    other: filteredSpeakers.filter((speaker) => speaker.category === 'other'),
  }

  const handleSpeakerClick = (speaker: Speaker) => {
    setSelectedSpeaker(selectedSpeaker?.id === speaker.id ? null : speaker)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
        {/* Large floating shapes */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-[#159147] rounded-full opacity-10 animate-random-float-1"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-[#81B1DB] rounded-full opacity-15 animate-random-float-2"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-[#E8B32C] rounded-full opacity-8 animate-random-float-3"></div>
        <div className="absolute top-1/3 right-1/3 w-28 h-28 bg-[#C86E36] rounded-full opacity-12 animate-random-float-4"></div>
        <div className="absolute bottom-20 right-10 w-36 h-36 bg-[#7E2518] rounded-full opacity-10 animate-random-float-5"></div>
        <div className="absolute top-60 left-1/2 w-20 h-20 bg-[#159147] rounded-full opacity-20 animate-random-float-6"></div>

        {/* Medium floating shapes */}
        <div className="absolute top-1/4 left-1/3 w-16 h-16 bg-[#81B1DB] rounded-full opacity-15 animate-random-float-2"></div>
        <div className="absolute bottom-1/4 right-1/4 w-12 h-12 bg-[#E8B32C] rounded-full opacity-20 animate-random-float-1"></div>
        <div className="absolute top-3/4 left-20 w-14 h-14 bg-[#C86E36] rounded-full opacity-12 animate-random-float-4"></div>
        <div className="absolute top-10 right-1/2 w-18 h-18 bg-[#7E2518] rounded-full opacity-15 animate-random-float-3"></div>

        {/* Small accent dots */}
        <div className="absolute top-1/2 left-10 w-8 h-8 bg-[#159147] rounded-full opacity-25 animate-subtle-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-6 h-6 bg-[#81B1DB] rounded-full opacity-30 animate-subtle-pulse"></div>
        <div className="absolute top-2/3 right-20 w-10 h-10 bg-[#E8B32C] rounded-full opacity-20 animate-subtle-pulse"></div>
        <div className="absolute bottom-10 left-1/2 w-4 h-4 bg-[#C86E36] rounded-full opacity-35 animate-subtle-pulse"></div>
        <div className="absolute top-20 left-2/3 w-12 h-12 bg-[#7E2518] rounded-full opacity-15 animate-subtle-pulse"></div>

        {/* Geometric shapes for variety */}
        <div
          className="absolute top-1/3 left-1/4 w-20 h-20 bg-[#159147] opacity-8 rotate-45 animate-random-float-5"
          style={{ clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)' }}
        ></div>
        <div
          className="absolute bottom-1/2 right-1/5 w-16 h-16 bg-[#81B1DB] opacity-12 animate-random-float-2"
          style={{ clipPath: 'polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%)' }}
        ></div>
        <div
          className="absolute top-2/3 left-1/2 w-24 h-24 bg-[#E8B32C] opacity-10 animate-random-float-6"
          style={{ clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)' }}
        ></div>
      </div>

      {/* Header */}

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 relative z-10">
        {/* Filters */}
        <div className="mb-12">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-center">
            <span className="text-gray-700 font-medium">Filter by:</span>
            <div className="flex flex-row gap-2 sm:gap-4 w-full lg:w-auto overflow-x-auto">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-32 sm:w-48 flex-shrink-0 border-[#81B1DB] focus:ring-[#159147]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="main">Main Speakers</SelectItem>
                  <SelectItem value="keynote">Keynote Speakers</SelectItem>
                  <SelectItem value="other">Other Speakers</SelectItem>
                </SelectContent>
              </Select>

              <Select value={speakerFilter} onValueChange={setSpeakerFilter}>
                <SelectTrigger className="w-32 sm:w-48 flex-shrink-0 border-[#81B1DB] focus:ring-[#159147]">
                  <SelectValue placeholder="Speaker" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Speakers</SelectItem>
                  {speakers.map((speaker) => (
                    <SelectItem key={speaker.id} value={speaker.name}>
                      {speaker.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={organizationFilter} onValueChange={setOrganizationFilter}>
                <SelectTrigger className="w-32 sm:w-48 flex-shrink-0 border-[#81B1DB] focus:ring-[#159147]">
                  <SelectValue placeholder="Organization" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Organizations</SelectItem>
                  {Array.from(new Set(speakers.map((s) => s.organization))).map((org) => (
                    <SelectItem key={org} value={org}>
                      {org}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={topicFilter} onValueChange={setTopicFilter}>
                <SelectTrigger className="w-32 sm:w-48 flex-shrink-0 border-[#81B1DB] focus:ring-[#159147]">
                  <SelectValue placeholder="Topic" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Topics</SelectItem>
                  {Array.from(new Set(speakers.map((s) => s.topic))).map((topic) => (
                    <SelectItem key={topic} value={topic}>
                      {topic}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold text-gray-300 mb-4">SPEAKERS</h1>
          <h2 className="text-4xl font-bold text-[#7E2518] mb-6">Who's Speaking ?</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Experience powerful keynote sessions, engage with leading product professionals, and
            enjoy exclusive networking opportunities throughout the event.
          </p>
        </div>

        {/* Speakers Sections */}
        <div className="space-y-16">
          {/* Main Speakers Section */}
          {groupedSpeakers.main.length > 0 && (
            <section>
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-[#159147] mb-2">MAIN SPEAKERS</h2>
                <div className="w-24 h-1 bg-[#159147] mx-auto rounded-full"></div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {groupedSpeakers.main.map((speaker) => (
                  <SpeakerCard
                    key={speaker.id}
                    speaker={speaker}
                    isSelected={selectedSpeaker?.id === speaker.id}
                    hasSelectedSpeaker={selectedSpeaker !== null}
                    onSpeakerClick={handleSpeakerClick}
                    formatDate={formatDate}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Keynote Speakers Section */}
          {groupedSpeakers.keynote.length > 0 && (
            <section>
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-[#E8B32C] mb-2">KEYNOTE SPEAKERS</h2>
                <div className="w-24 h-1 bg-[#E8B32C] mx-auto rounded-full"></div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {groupedSpeakers.keynote.map((speaker) => (
                  <SpeakerCard
                    key={speaker.id}
                    speaker={speaker}
                    isSelected={selectedSpeaker?.id === speaker.id}
                    hasSelectedSpeaker={selectedSpeaker !== null}
                    onSpeakerClick={handleSpeakerClick}
                    formatDate={formatDate}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Other Speakers Section */}
          {groupedSpeakers.other.length > 0 && (
            <section>
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-[#81B1DB] mb-2">OTHER SPEAKERS</h2>
                <div className="w-24 h-1 bg-[#81B1DB] mx-auto rounded-full"></div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {groupedSpeakers.other.map((speaker) => (
                  <SpeakerCard
                    key={speaker.id}
                    speaker={speaker}
                    isSelected={selectedSpeaker?.id === speaker.id}
                    hasSelectedSpeaker={selectedSpeaker !== null}
                    onSpeakerClick={handleSpeakerClick}
                    formatDate={formatDate}
                  />
                ))}
              </div>
            </section>
          )}
        </div>
      </main>


    </div>
  )
}
