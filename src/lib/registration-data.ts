// Shared data structures for registration system

export interface PackageOption {
  id: string
  name: string
  price: string
  duration?: string
  description: string
  features: string[]
  popular?: boolean
  category: 'delegate' | 'vip' | 'sponsor' | 'exhibitor'
}

export interface SponsorshipTier {
  id: string
  name: string
  price: string
  benefits: string[]
  delegateTickets: number
  vipTickets: number
  category: 'main' | 'special'
  description?: string
}

export interface RegistrationFormData {
  // Personal Information
  title?: string
  firstName: string
  lastName: string
  email: string
  phone: string
  organization?: string
  position?: string
  country: string
  city: string
  address?: string

  // Package/Tier Selection
  selectedPackage?: string
  selectedTier?: string

  // Group Registration
  isGroupRegistration?: boolean
  groupMembers?: RegistrationFormData[]

  // Additional fields based on registration type
  [key: string]: any
}

export interface QRCodeData {
  confirmationNumber: string
  registrantName: string
  email: string
  registrationType: string
  package: string
  eventDate: string
  venue: string
}

// Delegate Packages
export const delegatePackages: PackageOption[] = [
  {
    id: "basic",
    name: "Basic Package",
    price: "KES 15,000",
    duration: "3 days",
    description: "Essential conference access",
    category: "delegate",
    features: [
      "Access to all conference sessions",
      "Conference materials and program booklet",
      "Tea breaks for all days",
      "Certificate of participation",
      "Basic networking opportunities"
    ],
  },
  {
    id: "standard",
    name: "Standard Package",
    price: "KES 25,000",
    duration: "3 days",
    description: "Conference + enhanced networking",
    category: "delegate",
    popular: true,
    features: [
      "All Basic Package features",
      "Welcome dinner and networking sessions",
      "Gift pack with branded items",
      "Access to exhibition area",
      "Lunch for all days",
      "Priority registration desk access"
    ],
  },
  {
    id: "premium",
    name: "Premium Package",
    price: "KES 35,000",
    duration: "3 days",
    description: "Complete conference experience",
    category: "delegate",
    features: [
      "All Standard Package features",
      "Site visits to Indigenous Knowledge centers",
      "Premium gift pack with cultural souvenirs",
      "Priority seating in all sessions",
      "Access to VIP networking areas",
      "Post-conference resource access"
    ],
  },
]

// VIP Package
export const vipPackage: PackageOption = {
  id: "vip",
  name: "VIP Experience",
  price: "KES 30,000",
  duration: "3 days",
  description: "Premium experience for distinguished guests",
  category: "vip",
  features: [
    "Access to VIP dealrooms and holding areas",
    "Gala dinner and exclusive networking events",
    "Priority registration desk and VIP car badge",
    "Premium gift pack with flash drive",
    "All delegate benefits plus VIP amenities",
    "Downloadable VIP car sticker upon payment",
    "Dedicated liaison officer",
    "Priority seating and special recognition"
  ],
}

// Main Sponsorship Tiers
export const mainSponsorshipTiers: SponsorshipTier[] = [
  {
    id: "title",
    name: "Title Sponsor",
    price: "KES 1,000,000",
    category: "main",
    description: "Highest level of partnership and brand visibility",
    delegateTickets: 15,
    vipTickets: 15, // All tickets are VIP
    benefits: [
      "Title sponsor recognition in all materials",
      "Keynote speaking opportunity",
      "Premium exhibition space (6x6m)",
      "Logo on all conference materials",
      "15 complimentary VIP delegate passes",
      "Year-round partnership benefits",
      "Media interview opportunities",
      "Custom branding opportunities"
    ],
  },
  {
    id: "platinum",
    name: "Platinum Sponsor",
    price: "KES 500,000",
    category: "main",
    description: "Premium brand visibility and engagement",
    delegateTickets: 10,
    vipTickets: 5, // 5 VIP, 5 regular delegate
    benefits: [
      "Premium brand visibility",
      "Keynote speaking opportunity",
      "VIP networking access",
      "Custom exhibition space (4x4m)",
      "10 complimentary delegate passes (5 VIP)",
      "Year-round partnership",
      "Logo placement in prime locations",
      "Social media promotion"
    ],
  },
  {
    id: "gold",
    name: "Gold Sponsor",
    price: "KES 300,000",
    category: "main",
    description: "High visibility and networking privileges",
    delegateTickets: 5,
    vipTickets: 2, // 2 VIP, 3 regular delegate
    benefits: [
      "High brand visibility",
      "Panel discussion opportunity",
      "Networking privileges",
      "Exhibition space (3x3m)",
      "5 complimentary delegate passes (2 VIP)",
      "Marketing materials inclusion",
      "Conference program advertisement",
      "Welcome reception branding"
    ],
  },
  {
    id: "silver",
    name: "Silver Sponsor",
    price: "KES 150,000",
    category: "main",
    description: "Brand visibility and networking access",
    delegateTickets: 3,
    vipTickets: 1, // 1 VIP, 2 regular delegate
    benefits: [
      "Brand visibility",
      "Networking access",
      "Logo placement",
      "Conference materials inclusion",
      "3 complimentary delegate passes (1 VIP)",
      "Social media promotion",
      "Certificate of partnership",
      "Exhibition area access"
    ],
  },
  {
    id: "bronze",
    name: "Bronze Sponsor",
    price: "KES 75,000",
    category: "main",
    description: "Basic sponsorship with networking benefits",
    delegateTickets: 2,
    vipTickets: 0, // All regular delegate
    benefits: [
      "Logo placement",
      "Conference access",
      "Basic networking",
      "2 complimentary delegate passes",
      "Certificate of partnership",
      "Conference materials inclusion",
      "Social media mention"
    ],
  },
]

// Special Sponsorship Packages
export const specialSponsorshipTiers: SponsorshipTier[] = [
  {
    id: "media-partner",
    name: "Media Partner",
    price: "In-kind contribution",
    category: "special",
    description: "Media coverage and promotional partnership",
    delegateTickets: 2,
    vipTickets: 0,
    benefits: [
      "Media partner recognition",
      "Press conference access",
      "Interview opportunities with speakers",
      "2 complimentary delegate passes",
      "Logo on media materials",
      "Social media cross-promotion",
      "Post-event content sharing rights"
    ],
  },
  {
    id: "networking-reception",
    name: "Networking Reception Sponsor",
    price: "KES 200,000",
    category: "special",
    description: "Sponsor the main networking reception",
    delegateTickets: 4,
    vipTickets: 2,
    benefits: [
      "Exclusive branding at networking reception",
      "Welcome speech opportunity",
      "4 complimentary delegate passes (2 VIP)",
      "Logo on reception materials",
      "Branded welcome drinks",
      "Photo opportunities with VIPs",
      "Social media coverage"
    ],
  },
  {
    id: "merchandise",
    name: "Merchandise Sponsor",
    price: "KES 100,000",
    category: "special",
    description: "Sponsor conference merchandise and gift packs",
    delegateTickets: 3,
    vipTickets: 1,
    benefits: [
      "Logo on all conference merchandise",
      "Branded items in gift packs",
      "3 complimentary delegate passes (1 VIP)",
      "Merchandise distribution rights",
      "Brand visibility throughout event",
      "Social media promotion"
    ],
  },
  {
    id: "internet",
    name: "Internet & Technology Sponsor",
    price: "KES 80,000",
    category: "special",
    description: "Provide internet and technology services",
    delegateTickets: 2,
    vipTickets: 1,
    benefits: [
      "Technology sponsor recognition",
      "Logo on WiFi login pages",
      "2 complimentary delegate passes (1 VIP)",
      "Tech support acknowledgment",
      "Digital signage opportunities",
      "Innovation showcase participation"
    ],
  },
]

// All sponsorship tiers combined
export const allSponsorshipTiers = [...mainSponsorshipTiers, ...specialSponsorshipTiers]

// Countries list
export const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Canada",
  "Germany",
  "France",
  "Netherlands",
  "Sweden",
  "Norway",
  "Other",
]

// Business types for exhibitors
export const businessTypes = [
  "Traditional Foods & Nutrition",
  "Local Remedies & Traditional Medicine",
  "Musicology & Cultural Arts",
  "Cultural Tourism & Heritage",
  "Indigenous Technologies & Innovations",
  "Sui Generis Intellectual Property Systems",
  "Agricultural Innovations",
  "Craft & Artisan Products",
  "Educational Services",
  "Research & Development"
]

// Investment areas for investors
export const investmentAreas = [
  "Traditional Foods & Nutrition",
  "Local Remedies & Traditional Medicine",
  "Musicology & Cultural Arts",
  "Cultural Tourism & Heritage",
  "Indigenous Technologies & Innovations",
  "Sui Generis Intellectual Property Systems",
  "Agricultural Technology",
  "Sustainable Development",
  "Social Impact Ventures",
  "Cultural Preservation"
]

// Investment types
export const investmentTypes = [
  "Angel Investment",
  "Venture Capital",
  "Private Equity",
  "Impact Investment",
  "Grant Funding",
  "Strategic Partnership",
  "Microfinance",
  "Crowdfunding",
  "Government Funding",
  "Development Finance"
]

// Investment ranges
export const investmentRanges = [
  "Under KES 100,000",
  "KES 100,000 - KES 500,000",
  "KES 500,000 - KES 1,000,000",
  "KES 1,000,000 - KES 5,000,000",
  "KES 5,000,000 - KES 10,000,000",
  "Over KES 10,000,000",
]

// VIP Categories
export const vipCategories = [
  "Government Official",
  "Keynote Speaker",
  "Distinguished Guest",
  "Diplomatic Representative",
  "International Organization Representative",
  "Media Representative",
  "Academic Leader",
  "Industry Leader",
  "Cultural Leader",
  "Traditional Authority"
]

// Booth requirements for exhibitors
export const boothRequirements = [
  "Standard 3x3m booth",
  "Corner booth (additional cost)",
  "Double booth 6x3m (additional cost)",
  "Premium location (additional cost)",
  "Custom booth design (additional cost)"
]

// Additional services for exhibitors
export const additionalServices = [
  "Audio/Visual equipment",
  "Additional furniture",
  "Electrical connections",
  "Internet connectivity",
  "Storage space",
  "Promotional materials display",
  "Security services",
  "Catering services"
]

// Special services for VIPs
export const specialServices = [
  "Airport pickup/drop-off",
  "Dedicated liaison officer",
  "Translation services",
  "Special dietary requirements",
  "Accessibility accommodations",
  "Security arrangements",
  "Private meeting rooms",
  "Media interview coordination"
]

// Accessibility needs
export const accessibilityNeeds = [
  "Wheelchair accessibility",
  "Sign language interpretation",
  "Large print materials",
  "Audio assistance",
  "Mobility assistance",
  "Visual assistance",
  "Dietary accommodations",
  "Quiet spaces"
]

// Thematic areas
export const thematicAreas = [
  "Traditional Knowledge Systems",
  "Indigenous Innovation",
  "Cultural Heritage Preservation",
  "Sustainable Development",
  "Intellectual Property Rights",
  "Community-Based Research",
  "Traditional Medicine",
  "Agricultural Practices",
  "Environmental Conservation",
  "Cultural Arts & Music"
]
