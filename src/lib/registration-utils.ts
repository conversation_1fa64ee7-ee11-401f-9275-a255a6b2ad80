import QRCode from 'qrcode'
import { RegistrationFormData, QRCodeData, SponsorshipTier, allSponsorshipTiers } from './registration-data'

// Generate unique confirmation number
export function generateConfirmationNumber(registrationType: string): string {
  const prefix = getRegistrationPrefix(registrationType)
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.random().toString(36).substring(2, 6).toUpperCase()
  return `IKIA2025-${prefix}-${timestamp}${random}`
}

// Get registration type prefix
function getRegistrationPrefix(type: string): string {
  const prefixes: Record<string, string> = {
    'delegate': 'DEL',
    'vip': 'VIP',
    'sponsor': 'SPO',
    'exhibitor': 'EXH',
    'investor': 'INV'
  }
  return prefixes[type.toLowerCase()] || 'REG'
}

// Generate QR code data URL
export async function generateQRCode(data: QRCodeData): Promise<string> {
  const qrData = JSON.stringify({
    conf: data.confirmationNumber,
    name: data.registrantName,
    email: data.email,
    type: data.registrationType,
    pkg: data.package,
    event: data.eventDate,
    venue: data.venue,
    generated: new Date().toISOString()
  })

  try {
    const qrCodeDataURL = await QRCode.toDataURL(qrData, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    return qrCodeDataURL
  } catch (error) {
    console.error('Error generating QR code:', error)
    throw new Error('Failed to generate QR code')
  }
}

// Download QR code as image
export function downloadQRCode(dataURL: string, filename: string): void {
  const link = document.createElement('a')
  link.href = dataURL
  link.download = `${filename}.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Validate form data
export function validateRegistrationForm(data: RegistrationFormData, requiredFields: string[]): string[] {
  const errors: string[] = []
  
  requiredFields.forEach(field => {
    if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
      errors.push(`${field} is required`)
    }
  })

  // Email validation
  if (data.email && !isValidEmail(data.email)) {
    errors.push('Please enter a valid email address')
  }

  // Phone validation
  if (data.phone && !isValidPhone(data.phone)) {
    errors.push('Please enter a valid phone number')
  }

  return errors
}

// Email validation
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Phone validation
function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

// Get sponsorship tier details
export function getSponsorshipTier(tierId: string): SponsorshipTier | undefined {
  return allSponsorshipTiers.find(tier => tier.id === tierId)
}

// Calculate total delegate tickets for sponsor
export function calculateSponsorTickets(tierId: string): { total: number; vip: number; regular: number } {
  const tier = getSponsorshipTier(tierId)
  if (!tier) return { total: 0, vip: 0, regular: 0 }
  
  return {
    total: tier.delegateTickets,
    vip: tier.vipTickets,
    regular: tier.delegateTickets - tier.vipTickets
  }
}

// Group registration utilities
export interface GroupRegistrationState {
  isGroupMode: boolean
  currentMemberIndex: number
  members: RegistrationFormData[]
}

export function initializeGroupRegistration(): GroupRegistrationState {
  return {
    isGroupMode: false,
    currentMemberIndex: 0,
    members: []
  }
}

export function addGroupMember(
  state: GroupRegistrationState, 
  memberData: RegistrationFormData
): GroupRegistrationState {
  return {
    ...state,
    members: [...state.members, memberData],
    currentMemberIndex: state.members.length
  }
}

export function updateGroupMember(
  state: GroupRegistrationState,
  index: number,
  memberData: RegistrationFormData
): GroupRegistrationState {
  const updatedMembers = [...state.members]
  updatedMembers[index] = memberData
  return {
    ...state,
    members: updatedMembers
  }
}

export function removeGroupMember(
  state: GroupRegistrationState,
  index: number
): GroupRegistrationState {
  const updatedMembers = state.members.filter((_, i) => i !== index)
  return {
    ...state,
    members: updatedMembers,
    currentMemberIndex: Math.min(state.currentMemberIndex, updatedMembers.length - 1)
  }
}

export function navigateToMember(
  state: GroupRegistrationState,
  index: number
): GroupRegistrationState {
  return {
    ...state,
    currentMemberIndex: Math.max(0, Math.min(index, state.members.length - 1))
  }
}

// Form data persistence utilities
export function saveFormDataToStorage(key: string, data: any): void {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.warn('Failed to save form data to localStorage:', error)
  }
}

export function loadFormDataFromStorage(key: string): any | null {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.warn('Failed to load form data from localStorage:', error)
    return null
  }
}

export function clearFormDataFromStorage(key: string): void {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.warn('Failed to clear form data from localStorage:', error)
  }
}

// Format currency
export function formatCurrency(amount: string): string {
  if (amount.toLowerCase().includes('in-kind')) {
    return amount
  }
  
  // Extract numeric value
  const numericValue = amount.replace(/[^\d,]/g, '')
  if (!numericValue) return amount
  
  // Format with commas
  const formatted = numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return amount.replace(numericValue, formatted)
}

// Generate registration summary
export function generateRegistrationSummary(data: RegistrationFormData, registrationType: string): any {
  return {
    confirmationNumber: generateConfirmationNumber(registrationType),
    registrantName: `${data.firstName} ${data.lastName}`,
    email: data.email,
    phone: data.phone,
    organization: data.organization || 'N/A',
    registrationType: registrationType,
    package: data.selectedPackage || data.selectedTier || 'N/A',
    registrationDate: new Date().toLocaleDateString(),
    isGroupRegistration: data.isGroupRegistration || false,
    groupSize: data.groupMembers?.length || 1
  }
}

// Event details for QR code
export const eventDetails = {
  name: "IKIA Conference 2025",
  date: "March 15-17, 2025",
  venue: "Kenyatta International Convention Centre, Nairobi",
  organizer: "Indigenous Knowledge & Innovation Africa"
}
