'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { ChevronDown, TrendingUp, Users, Star, Building } from 'lucide-react'

interface InvestDropdownProps {
  className?: string
}

export default function InvestDropdown({ className = '' }: InvestDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)

  const dropdownItems = [
    {
      href: '/invest',
      title: 'Investment Overview',
      description: 'Explore investment opportunities in Indigenous Knowledge',
      icon: TrendingUp,
      color: 'text-[#7E2518]'
    },
    {
      href: '/invest/investors',
      title: 'All Investors',
      description: 'Connect with verified impact investors',
      icon: Building,
      color: 'text-[#7E2518]'
    },
    {
      href: '/invest/exhibitors',
      title: 'All Exhibitors',
      description: 'Discover Indigenous Knowledge holders',
      icon: Users,
      color: 'text-[#159147]'
    },
    {
      href: '/invest/success-stories',
      title: 'Success Stories',
      description: 'Real partnerships creating sustainable impact',
      icon: Star,
      color: 'text-[#E8B32C]'
    }
  ]

  return (
    <div 
      className={`relative ${className}`}
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      {/* Trigger */}
      <div className="relative px-4 py-2 text-white/90 hover:text-white transition-all duration-300 font-medium text-base tracking-wide group cursor-pointer">
        <span className="relative z-10 flex items-center gap-1">
          Invest
          <ChevronDown 
            className={`w-4 h-4 transition-transform duration-300 ${
              isOpen ? 'rotate-180' : ''
            }`} 
          />
        </span>
        {/* Hover background effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#159147]/20 to-[#E8B32C]/20 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 origin-center"></div>
      </div>

      {/* Dropdown Menu */}
      <div 
        className={`absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden transition-all duration-300 z-50 ${
          isOpen 
            ? 'opacity-100 visible transform translate-y-0' 
            : 'opacity-0 invisible transform -translate-y-2'
        }`}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-[#7E2518] to-[#C86E36] px-6 py-4">
          <h3 className="text-white font-bold text-lg">Investment Platform</h3>
          <p className="text-white/80 text-sm">Connecting investors with Indigenous Knowledge</p>
        </div>

        {/* Menu Items */}
        <div className="py-2">
          {dropdownItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="block px-6 py-4 hover:bg-gray-50 transition-colors duration-200 group"
            >
              <div className="flex items-start gap-4">
                <div className={`p-2 rounded-lg bg-gray-100 group-hover:bg-gray-200 transition-colors ${item.color}`}>
                  <item.icon className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 group-hover:text-[#7E2518] transition-colors">
                    {item.title}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {item.description}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-100">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">Ready to get started?</p>
            <Link 
              href="/invest"
              className="inline-flex items-center justify-center px-4 py-2 bg-[#7E2518] text-white text-sm font-medium rounded-lg hover:bg-[#7E2518]/90 transition-colors"
            >
              Explore Opportunities
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
