'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Menu, X } from 'lucide-react'
import Image from 'next/image'
import { ThemeToggle } from '@/components/theme-toggle'
import Link from 'next/link'

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navItems = [
    { name: 'Home', href: '/' },
    { name: 'Exhibitions', href: '/exhibitions' },
    { name: 'About', href: '/about' },
    { name: 'Resources', href: '/resources' },
    { name: 'Partners & Sponsors', href: '/partners-sponsors' },
    { name: 'Invest', href: '/invest' },
    { name: 'News', href: '/news-media' },
    { name: 'Contact', href: '/contact' },
  ]

  return (
    <nav className={`sticky top-0 z-50 transition-all duration-300 bg-gradient-to-r from-[#22c55e] to-[#16a34a]`}>
      {/* Subtle overlay pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>

      <div className="container mx-auto px-4 relative">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex-shrink-0 z-10">
            <div className="bg-transparent backdrop-transparent rounded-lg p-2 hover:bg-white/25 transition-all duration-300 border border-transparent">
              <Image
                src="/logo.png"
                alt="IKIA - Indigenous Knowledge and Intellectual Assets"
                width={200}
                height={60}
                className="h-8 lg:h-10 w-auto filter brightness-110"
                priority
              />
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="relative px-4 py-2 text-white/90 hover:text-white transition-all duration-300 font-medium text-base tracking-wide group"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                <span className="relative z-10">{item.name}</span>
                {/* Hover background effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-[#159147]/20 to-[#E8B32C]/20 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300 origin-center"></div>
                {/* Active indicator */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-[#E8B32C] group-hover:w-full transition-all duration-300"></div>
              </a>
            ))}
          </div>

          {/* Desktop Action Buttons */}
          <div className="hidden lg:flex items-center space-x-3">
            <ThemeToggle />
            <a href="/registration">
              <Button
                className="bg-gradient-to-r from-[#159147] to-[#1ba854] hover:from-[#1ba854] hover:to-[#159147] text-white border-0 px-6 py-2 rounded-lg font-semibold tracking-wide transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:shadow-[#159147]/25"
                style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
              >
                Register Now
              </Button>
            </a>
            <Button
              className="bg-gradient-to-r from-[#E8B32C] to-[#C86E36] hover:from-[#C86E36] hover:to-[#E8B32C] text-white border-0 px-6 py-2 rounded-lg font-semibold tracking-wide transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 hover:shadow-[#E8B32C]/25"
              style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
            >
              Sponsor us
            </Button>
          </div>

          {/* Mobile Menu Button and Theme Toggle */}
          <div className="lg:hidden flex items-center space-x-2">
            <ThemeToggle />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-white hover:text-white hover:bg-white/20 p-2 rounded-lg transition-all duration-300"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        <div
          className={`lg:hidden overflow-hidden transition-all duration-300 ${
            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="bg-gradient-to-r from-[#16a34a]/95 to-[#22c55e]/95 backdrop-blur-md rounded-lg mx-2 mb-4 border border-white/20">
            <div className="px-4 py-3 space-y-2">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-4 py-3 text-white/90 hover:text-white hover:bg-gradient-to-r hover:from-[#159147]/20 hover:to-[#E8B32C]/20 rounded-lg font-medium transition-all duration-300 transform hover:translate-x-1"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="flex items-center justify-between">
                    {item.name}
                    <div className="w-2 h-2 bg-[#E8B32C] rounded-full opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                </a>
              ))}
              <div className="pt-3 border-t border-white/20 space-y-2">
                <Link href="/registration" className="block">
                  <Button
                    className="w-full bg-gradient-to-r from-[#159147] to-[#1ba854] hover:from-[#1ba854] hover:to-[#159147] text-white border-0 rounded-lg font-semibold tracking-wide transition-all duration-300"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    Register Now
                  </Button>
                </Link>

                <Link href="/sponsor" className="block">
                  <Button
                    className="w-full bg-gradient-to-r from-[#E8B32C] to-[#C86E36] hover:from-[#C86E36] hover:to-[#E8B32C] text-white border-0 rounded-lg font-semibold tracking-wide transition-all duration-300"
                    style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                  >
                    Sponsor us
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom accent line */}
      <div className="h-1 bg-gradient-to-r from-[#E8B32C] via-[#C86E36] to-[#81B1DB]"></div>
    </nav>
  )
}
