"use client"

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Star } from "lucide-react"
import { PackageOption } from '@/lib/registration-data'
import { formatCurrency } from '@/lib/registration-utils'

interface PackageGridProps {
  packages: PackageOption[]
  selectedPackage: string
  onPackageSelect: (packageId: string) => void
  className?: string
}

export function PackageGrid({ packages, selectedPackage, onPackageSelect, className = "" }: PackageGridProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {packages.map((pkg) => (
        <Card
          key={pkg.id}
          className={`relative cursor-pointer transition-all duration-300 hover:shadow-lg ${
            selectedPackage === pkg.id
              ? 'ring-2 ring-blue-500 shadow-lg'
              : 'hover:shadow-md'
          }`}
          onClick={() => onPackageSelect(pkg.id)}
        >
          {pkg.popular && (
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-orange-500 text-white px-3 py-1">
                <Star className="w-3 h-3 mr-1" />
                Most Popular
              </Badge>
            </div>
          )}
          
          <CardContent className="p-6">
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold text-gray-900 mb-2" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
                {pkg.name}
              </h3>
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {formatCurrency(pkg.price)}
              </div>
              {pkg.duration && (
                <div className="text-sm text-gray-500">
                  {pkg.duration}
                </div>
              )}
              <p className="text-gray-600 text-sm mt-2">
                {pkg.description}
              </p>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 text-sm">What's included:</h4>
              <ul className="space-y-2">
                {pkg.features.map((feature, index) => (
                  <li key={index} className="flex items-start text-sm text-gray-700">
                    <Check className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            {selectedPackage === pkg.id && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center text-blue-800">
                  <Check className="w-4 h-4 mr-2" />
                  <span className="text-sm font-medium">Selected Package</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

interface SponsorshipTierGridProps {
  tiers: any[]
  selectedTier: string
  onTierSelect: (tierId: string) => void
  className?: string
}

export function SponsorshipTierGrid({ tiers, selectedTier, onTierSelect, className = "" }: SponsorshipTierGridProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-6 ${className}`}>
      {tiers.map((tier) => (
        <Card
          key={tier.id}
          className={`relative cursor-pointer transition-all duration-300 hover:shadow-lg ${
            selectedTier === tier.id
              ? 'ring-2 ring-orange-500 shadow-lg'
              : 'hover:shadow-md'
          }`}
          onClick={() => onTierSelect(tier.id)}
        >
          {tier.category === 'main' && tier.id === 'platinum' && (
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-orange-500 text-white px-3 py-1">
                <Star className="w-3 h-3 mr-1" />
                Recommended
              </Badge>
            </div>
          )}
          
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
                {tier.name}
              </h3>
              <div className="text-right">
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(tier.price)}
                </div>
                {tier.delegateTickets > 0 && (
                  <div className="text-xs text-gray-500">
                    {tier.delegateTickets} tickets included
                  </div>
                )}
              </div>
            </div>

            {tier.description && (
              <p className="text-gray-600 text-sm mb-4">
                {tier.description}
              </p>
            )}

            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 text-sm">Benefits included:</h4>
              <ul className="space-y-2">
                {tier.benefits.map((benefit: string, index: number) => (
                  <li key={index} className="flex items-start text-sm text-gray-700">
                    <Check className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>

            {tier.delegateTickets > 0 && (
              <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="text-sm text-gray-700">
                  <strong>Complimentary Tickets:</strong>
                  <div className="mt-1">
                    {tier.vipTickets > 0 && (
                      <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-2">
                        {tier.vipTickets} VIP
                      </span>
                    )}
                    {(tier.delegateTickets - tier.vipTickets) > 0 && (
                      <span className="inline-block bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                        {tier.delegateTickets - tier.vipTickets} Delegate
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}

            {selectedTier === tier.id && (
              <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center text-orange-800">
                  <Check className="w-4 h-4 mr-2" />
                  <span className="text-sm font-medium">Selected Sponsorship</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
