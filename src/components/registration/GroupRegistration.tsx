"use client"

import React from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  ChevronLeft, 
  ChevronRight, 
  X, 
  Users, 
  User,
  AlertCircle
} from "lucide-react"
import { GroupRegistrationState } from '@/lib/registration-utils'

interface GroupRegistrationProps {
  groupState: GroupRegistrationState
  onToggleGroupMode: (enabled: boolean) => void
  onAddMember: () => void
  onNavigateToMember: (index: number) => void
  onRemoveMember: (index: number) => void
  currentMemberData?: any
  className?: string
}

export function GroupRegistration({
  groupState,
  onToggleGroupMode,
  onAddMember,
  onNavigateToMember,
  onRemoveMember,
  currentMemberData,
  className = ""
}: GroupRegistrationProps) {
  const { isGroupMode, currentMemberIndex, members } = groupState

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="w-5 h-5" />
          <span>Group Registration</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Group Mode Toggle */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <Switch
              id="group-mode"
              checked={isGroupMode}
              onCheckedChange={onToggleGroupMode}
            />
            <Label htmlFor="group-mode" className="font-medium">
              Enable Group Registration
            </Label>
          </div>
          {isGroupMode && (
            <Badge variant="secondary" className="ml-2">
              {members.length + 1} {members.length === 0 ? 'Member' : 'Members'}
            </Badge>
          )}
        </div>

        {isGroupMode && (
          <>
            {/* Group Navigation */}
            {members.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-900">
                    Registrant {currentMemberIndex + 1} of {members.length + 1}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onNavigateToMember(currentMemberIndex - 1)}
                      disabled={currentMemberIndex === 0}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onNavigateToMember(currentMemberIndex + 1)}
                      disabled={currentMemberIndex >= members.length}
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Member Navigation Dots */}
                <div className="flex items-center justify-center space-x-2">
                  {Array.from({ length: members.length + 1 }, (_, index) => (
                    <button
                      key={index}
                      onClick={() => onNavigateToMember(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === currentMemberIndex
                          ? 'bg-blue-600'
                          : index < members.length
                          ? 'bg-green-500'
                          : 'bg-gray-300'
                      }`}
                      title={`Registrant ${index + 1}${index < members.length ? ' (Completed)' : index === currentMemberIndex ? ' (Current)' : ' (New)'}`}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Current Member Info */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <User className="w-4 h-4 text-blue-600" />
                <span className="font-medium text-blue-900">
                  {currentMemberIndex === members.length 
                    ? 'Adding New Member' 
                    : `Editing Member ${currentMemberIndex + 1}`
                  }
                </span>
              </div>
              {currentMemberData?.firstName && currentMemberData?.lastName && (
                <p className="text-sm text-blue-700">
                  {currentMemberData.firstName} {currentMemberData.lastName}
                  {currentMemberData.email && ` (${currentMemberData.email})`}
                </p>
              )}
            </div>

            {/* Group Members List */}
            {members.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900">Registered Members</h4>
                <div className="space-y-2">
                  {members.map((member, index) => (
                    <div
                      key={index}
                      className={`flex items-center justify-between p-3 border rounded-lg transition-colors ${
                        index === currentMemberIndex
                          ? 'border-blue-300 bg-blue-50'
                          : 'border-gray-200 bg-white hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                          index === currentMemberIndex
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-700'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {member.firstName} {member.lastName}
                          </p>
                          <p className="text-sm text-gray-500">{member.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onNavigateToMember(index)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemoveMember(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Add Member Button */}
            {currentMemberIndex === members.length && (
              <div className="flex justify-center">
                <Button
                  onClick={onAddMember}
                  className="flex items-center space-x-2"
                  disabled={!currentMemberData?.firstName || !currentMemberData?.lastName || !currentMemberData?.email}
                >
                  <Plus className="w-4 h-4" />
                  <span>Add Another Registrant</span>
                </Button>
              </div>
            )}

            {/* Group Registration Info */}
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <AlertCircle className="w-4 h-4 text-amber-600 mt-0.5" />
                <div className="text-sm text-amber-800">
                  <p className="font-medium mb-1">Group Registration Notes:</p>
                  <ul className="space-y-1 text-xs">
                    <li>• Complete each member's information before adding the next</li>
                    <li>• You can edit or remove members using the controls above</li>
                    <li>• All members will be registered under the same package/tier</li>
                    <li>• Payment will be calculated for all group members</li>
                  </ul>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
