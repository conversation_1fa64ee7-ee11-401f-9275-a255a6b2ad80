import tailwindcssAnimate from 'tailwindcss-animate'
import typography from '@tailwindcss/typography'

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  darkMode: ['selector', '[data-theme="dark"]'],
  plugins: [tailwindcssAnimate, typography],
  prefix: '',
  safelist: [
    'lg:col-span-4',
    'lg:col-span-6',
    'lg:col-span-8',
    'lg:col-span-12',
    'border-border',
    'bg-card',
    'border-error',
    'bg-error/30',
    'border-success',
    'bg-success/30',
    'border-warning',
    'bg-warning/30',
  ],
  theme: {
    container: {
      center: true,
      padding: {
        '2xl': '2rem',
        DEFAULT: '1rem',
        lg: '2rem',
        md: '2rem',
        sm: '1rem',
        xl: '2rem',
      },
      screens: {
        '2xl': '86rem',
        lg: '64rem',
        md: '48rem',
        sm: '40rem',
        xl: '80rem',
      },
    },
    extend: {
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        acquire: ['ACQUIRE', 'Times New Roman', 'serif'],
        myriad: ['Myriad Pro', 'Inter', 'system-ui', 'sans-serif'],
        mono: ['var(--font-geist-mono)'],
        sans: ['var(--font-geist-sans)'],
      },
      colors: {
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        background: 'hsl(var(--background))',
        border: 'hsla(var(--border))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        foreground: 'hsl(var(--foreground))',
        input: 'hsl(var(--input))',
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        ring: 'hsl(var(--ring))',
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        success: 'hsl(var(--success))',
        error: 'hsl(var(--error))',
        warning: 'hsl(var(--warning))',
        // IKIA Brand Colors
        'ikia-green': {
          50: '#f4fdf6',
          100: '#e8fbed',
          200: '#ccf5d7',
          300: '#a3edb7',
          400: '#72dd8e',
          500: '#159147', // Base color
          600: '#0f7738',
          700: '#0c5d2c',
          800: '#094421',
          900: '#062d17',
          950: '#041e10',
        },
        'ikia-brown': {
          50: '#fdf8f7',
          100: '#f8eeec',
          200: '#efd9d4',
          300: '#e1bdb2',
          400: '#cf9883',
          500: '#7E2518', // Base color
          600: '#651e13',
          700: '#4d170f',
          800: '#36100b',
          900: '#240b07',
          950: '#160605',
        },
        'ikia-yellow': {
          50: '#fefdf4',
          100: '#fdfae8',
          200: '#fbf2cc',
          300: '#f7e8a3',
          400: '#f2dc72',
          500: '#E8B32C', // Base color
          600: '#cc9518',
          700: '#a77514',
          800: '#825810',
          900: '#5e3c0c',
          950: '#3d2608',
        },
        'ikia-sienna': {
          50: '#fdf8f6',
          100: '#f8edea',
          200: '#efd5ce',
          300: '#e3b7a9',
          400: '#d5917b',
          500: '#C85E36', // Base color
          600: '#a84b2b',
          700: '#853a21',
          800: '#622918',
          900: '#411b10',
          950: '#29100a',
        },
        'ikia-blue': {
          50: '#f6fafe',
          100: '#ecf4fc',
          200: '#d4e6f8',
          300: '#b6d4f2',
          400: '#91beea',
          500: '#81B1D8', // Base color
          600: '#6694c4',
          700: '#4f77a6',
          800: '#3b5a82',
          900: '#2a3e5e',
          950: '#1c293e',
        },
        // Chart colors using IKIA palette
        chart: {
          1: '#159147', // IKIA Green
          2: '#7E2518', // IKIA Brown
          3: '#E8B32C', // IKIA Yellow
          4: '#C85E36', // IKIA Sienna
          5: '#81B1D8', // IKIA Blue
        },
        // Sidebar colors
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
        },
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      typography: () => ({
        DEFAULT: {
          css: [
            {
              '--tw-prose-body': 'var(--text)',
              '--tw-prose-headings': 'var(--text)',
              h1: {
                fontWeight: 'normal',
                marginBottom: '0.25em',
              },
            },
          ],
        },
        base: {
          css: [
            {
              h1: {
                fontSize: '2.5rem',
              },
              h2: {
                fontSize: '1.25rem',
                fontWeight: 600,
              },
            },
          ],
        },
        md: {
          css: [
            {
              h1: {
                fontSize: '3.5rem',
              },
              h2: {
                fontSize: '1.5rem',
              },
            },
          ],
        },
      }),
    },
  },
}

export default config
